#define MyAppName "OCR Intelligent"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Votre Organisation"
#define MyAppURL "https://votre-site.com"
#define MyAppExeName "OCR_Intelligent.exe"
#define MyAppAssocName MyAppName + " Document"
#define MyAppAssocExt ".ocr"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId in installers for other applications.
AppId={{A1B2C3D4-E5F6-7890-A1B2-C3D4E5F67890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
ChangesAssociations=yes
DisableProgramGroupPage=yes
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputDir=dist
OutputBaseFilename=OCR_Intelligent_Setup
SetupIconFile=ocr_icon.ico
Compression=lzma2/ultra64
SolidCompression=yes
WizardStyle=modern
; Langue française par défaut
DefaultLanguage=fr
; Vérification des prérequis
MinVersion=10.0.17763
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "fr"; MessagesFile: "compiler:Languages\French.isl"
Name: "en"; MessagesFile: "compiler:Default.isl"

[CustomMessages]
fr.WelcomeLabel1=Bienvenue dans l'assistant d'installation de %1
fr.WelcomeLabel2=Cet assistant va installer %1 version %2 sur votre ordinateur.%n%nIl est recommandé de fermer toutes les autres applications avant de continuer.
fr.LicenseLabel1=Contrat de licence
fr.LicenseLabel3=Veuillez lire le contrat de licence suivant. Vous devez accepter les termes de ce contrat avant de continuer l'installation.
fr.LicenseAccepted=J'&accepte les termes du contrat de licence
fr.LicenseNotAccepted=Je n'&accepte pas les termes du contrat de licence
fr.ReadyLabel1=L'assistant est prêt à installer %1 sur votre ordinateur.
fr.ReadyLabel2a=Cliquez sur Installer pour continuer l'installation, ou sur Précédent si vous voulez revoir ou modifier les paramètres d'installation.
fr.ReadyLabel2b=Cliquez sur Installer pour continuer l'installation.
fr.InstallingLabel=Veuillez patienter pendant que l'assistant installe %1 sur votre ordinateur.
fr.FinishedLabel=L'assistant a terminé l'installation de %1 sur votre ordinateur.
fr.ClickFinish=Cliquez sur Terminer pour quitter l'assistant d'installation.
fr.FinishedRestartLabel=Pour terminer l'installation de %1, l'assistant doit redémarrer votre ordinateur. Voulez-vous redémarrer maintenant ?
fr.FinishedRestartMessage=Pour terminer l'installation de %1, l'assistant doit redémarrer votre ordinateur.%n%nVoulez-vous redémarrer maintenant ?
fr.DiskSpaceGBLabel=L'installation nécessite au moins %1 Go d'espace disque disponible.
fr.DiskSpaceMBLabel=L'installation nécessite au moins %1 Mo d'espace disque disponible.
fr.ComponentsLabel=Sélectionnez les composants à installer :
fr.ComponentsSubtitle=Choisissez les composants que vous souhaitez installer.
fr.NoUninstallWarningTitle=Composants existants
fr.NoUninstallWarning=L'assistant a détecté que les composants suivants sont déjà installés sur votre ordinateur :%n%n%1%n%nDésélectionner ces composants ne les désinstallera pas.%n%nVoulez-vous continuer quand même ?
fr.DownloadingLabel=Téléchargement en cours...
fr.ButtonBack=< &Précédent
fr.ButtonNext=&Suivant >
fr.ButtonInstall=&Installer
fr.ButtonCancel=Annuler
fr.ButtonYes=&Oui
fr.ButtonYesToAll=Oui pour &tout
fr.ButtonNo=&Non
fr.ButtonNoToAll=N&on pour tout
fr.ButtonFinish=&Terminer
fr.ButtonBrowse=&Parcourir...
fr.BrowseDialogTitle=Rechercher un dossier
fr.BrowseDialogLabel=Sélectionnez un dossier dans la liste ci-dessous, puis cliquez sur OK.
fr.UninstallAppFullName=Désinstaller %1
fr.UninstallAppNameShort=Désinstaller %1
fr.UninstallingAppName=Désinstallation de %1...
fr.ConfirmUninstall=Êtes-vous sûr de vouloir supprimer complètement %1 et tous ses composants ?
fr.UninstalledAll=%1 a été supprimé avec succès de votre ordinateur.
fr.UninstalledMost=%1 a été supprimé avec succès de votre ordinateur.%n%nCertains éléments n'ont pas pu être supprimés. Vous pouvez les supprimer manuellement.
fr.UninstalledAndNeedsRestart=Pour terminer la désinstallation de %1, votre ordinateur doit être redémarré.%n%nVoulez-vous redémarrer maintenant ?
fr.NotEnoughDiskSpace=Il n'y a pas assez d'espace disque disponible sur le lecteur sélectionné pour l'installation.
fr.DirExists=Le dossier:%n%1%nexiste déjà. Voulez-vous quand même installer dans ce dossier ?
fr.DirDoesntExist=Le dossier:%n%1%nn'existe pas. Voulez-vous créer ce dossier ?
fr.WizardSelectDir=Sélectionnez le dossier d'installation
fr.SelectDirLabel3=L'assistant va installer %1 dans le dossier suivant.
fr.SelectDirBrowseLabel=Pour continuer, cliquez sur Suivant. Si vous souhaitez sélectionner un dossier différent, cliquez sur Parcourir.
fr.DiskSpaceWarningTitle=Espace disque insuffisant
fr.DiskSpaceWarning=L'installation nécessite au moins %1 Ko d'espace libre, mais le lecteur sélectionné n'a que %2 Ko disponibles.%n%nVoulez-vous continuer quand même ?
fr.DirNameTooLong=Le nom ou le chemin du dossier est trop long.
fr.InvalidDirName=Le nom du dossier n'est pas valide.
fr.BadDirName32=Les noms de dossiers ne peuvent pas inclure les caractères suivants :%n%n%1
fr.DirExistsTitle=Le dossier existe
fr.DirExists=Le dossier:%n%1%nexiste déjà. Voulez-vous quand même installer dans ce dossier ?
fr.DirDoesntExistTitle=Le dossier n'existe pas
fr.DirDoesntExist=Le dossier:%n%1%nn'existe pas. Voulez-vous créer ce dossier ?
fr.WizardSelectComponents=Sélectionnez les composants
fr.SelectComponentsDesc=Quels composants souhaitez-vous installer ?
fr.SelectComponentsLabel2=Sélectionnez les composants à installer ; décochez les composants que vous ne voulez pas installer. Cliquez sur Suivant lorsque vous êtes prêt à continuer.
fr.FullInstallation=Installation complète
fr.CompactInstallation=Installation compacte
fr.CustomInstallation=Installation personnalisée
fr.NoUninstallWarningTitle=Composants existants
fr.NoUninstallWarning=L'assistant a détecté que les composants suivants sont déjà installés sur votre ordinateur :%n%n%1%n%nDésélectionner ces composants ne les désinstallera pas.%n%nVoulez-vous continuer quand même ?
fr.MemoryTooLowError=Vous n'avez pas assez de mémoire pour exécuter cette installation.
fr.RequiredRAM=L'application nécessite au moins 4 Go de RAM.
fr.RequiredDisk=L'application nécessite au moins 2 Go d'espace disque.
fr.LaunchApp=Lancer OCR Intelligent après l'installation

en.WelcomeLabel1=Welcome to the %1 Setup Wizard
en.WelcomeLabel2=This will install %1 version %2 on your computer.%n%nIt is recommended that you close all other applications before continuing.
en.RequiredRAM=The application requires at least 4 GB of RAM.
en.RequiredDisk=The application requires at least 2 GB of disk space.
en.LaunchApp=Launch OCR Intelligent after installation

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
Source: "dist\OCR_Intelligent\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "ocr_icon.ico"; DestDir: "{app}"; Flags: ignoreversion
Source: "documentation\*"; DestDir: "{app}\documentation"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\ocr_icon.ico"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\ocr_icon.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\ocr_icon.ico"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchApp}"; Flags: nowait postinstall skipifsilent

[Code]
function InitializeSetup(): Boolean;
var
  RAMSizeInGB: Integer;
  FreeDiskSpaceInGB: Integer;
  RequiredRAMInGB: Integer;
  RequiredDiskSpaceInGB: Integer;
  ErrorMessage: String;
  RAMOk, DiskOk: Boolean;
begin
  // Vérification de la RAM (4 Go minimum)
  RequiredRAMInGB := 4;
  RAMSizeInGB := Round(GetPhysicalMemoryInMB() / 1024);
  RAMOk := RAMSizeInGB >= RequiredRAMInGB;
  
  // Vérification de l'espace disque (2 Go minimum)
  RequiredDiskSpaceInGB := 2;
  FreeDiskSpaceInGB := Round(GetSpaceOnDisk('C:', True) / (1024*1024*1024));
  DiskOk := FreeDiskSpaceInGB >= RequiredDiskSpaceInGB;
  
  // Afficher un message si les prérequis ne sont pas satisfaits
  if not (RAMOk and DiskOk) then
  begin
    ErrorMessage := '';
    
    if not RAMOk then
      ErrorMessage := ErrorMessage + CustomMessage('RequiredRAM') + #13#10;
      
    if not DiskOk then
      ErrorMessage := ErrorMessage + CustomMessage('RequiredDisk') + #13#10;
      
    ErrorMessage := ErrorMessage + #13#10 + 'Voulez-vous continuer quand même ?';
    
    if MsgBox(ErrorMessage, mbConfirmation, MB_YESNO) = IDNO then
      Result := False
    else
      Result := True;
  end
  else
    Result := True;
end;

[UninstallDelete]
Type: filesandordirs; Name: "{app}\*"
