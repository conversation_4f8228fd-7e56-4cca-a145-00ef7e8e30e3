# Structure du Projet OCR Intelligent

## 📁 Organisation Finale

```
OCR_Intelligent/
├── 📄 README.md                          # Documentation principale
├── 📄 requirements.txt                   # Dépendances Python
├── 🚀 main.py                           # Point d'entrée principal
├── ⚙️ config.py                         # Configuration
├── 🔧 port_manager.py                   # Gestion des ports
├── 🚀 Lancer_OCR_Intelligent.bat        # Lanceur principal
├── 📦 OCR_Intelligent_Setup.iss         # Script installateur
│
├── backend/                              # Modules backend
│   ├── 🎯 layout_detector.py           # Détection de mise en page
│   ├── 📊 structured_ocr.py            # OCR structuré
│   ├── 🔧 preprocessing.py             # Préprocessing adaptatif
│   ├── 🚀 main.py                      # Pipeline principal
│   ├── 📄 export.py                    # Export Word/JSON
│   ├── 🔧 ocr_tesseract.py            # OCR Tesseract
│   ├── 🤖 ocr_easyocr.py              # OCR EasyOCR
│   ├── 📄 ocr_doctr.py                # OCR DocTR
│   ├── ✏️ corrector.py                 # Correction automatique
│   └── 📊 quality_evaluator.py        # Évaluation qualité
│
├── frontend/                             # Interface utilisateur
│   ├── 🖥️ app.py                       # Interface Streamlit
│   ├── 🎨 custom_style.html           # Styles CSS
│   └── 🖼️ safran_logo.png             # Logo
│
├── models/                               # Modèles OCR
│   ├── tesseract/                      # Modèles Tesseract
│   ├── easyocr/                        # Modèles EasyOCR
│   ├── doctr/                          # Modèles DocTR
│   └── paddleocr/                      # Modèles PaddleOCR
│
├── images/                               # Images d'exemple
│   ├── exemple1.png
│   ├── exemple2.jpg
│   ├── facture1.png
│   ├── FACTURE-ARTFORDPLUS_N1-1.jpg
│   └── modele-facture-fr-bande-bleu-750px.png
│
├── tools/                                # Outils et utilitaires
│   ├── 📦 install_layout_detection.bat # Installation détection mise en page
│   ├── 🧪 test_simple.py              # Test rapide
│   └── 🔧 build_installer.bat         # Construction installateur
│
├── docs/                                 # Documentation
│   ├── 🎯 LAYOUT_DETECTION_GUIDE.md   # Guide détection mise en page
│   └── 📁 PROJECT_STRUCTURE.md        # Ce fichier
│
├── output/                               # Dossier de sortie (auto-nettoyé)
│   ├── backend/                        # Fichiers backend temporaires
│   └── debug/                          # Images de debug
│
├── logs/                                 # Logs application
│   └── 📝 main.log
│
├── corrected/                            # Corrections utilisateur
│
├── dist/                                 # Distribution
│   └── OCR_Intelligent_Setup_v2.0.0.exe
│
└── exe/                                  # Exécutable portable
    └── OCR_Streamlit_Portable/
```

## 🎯 Fonctionnalités Principales

### OCR Classique
- **3 moteurs** : Tesseract, EasyOCR, DocTR
- **Comparaison automatique** des résultats
- **Export Word** avec mise en forme

### Détection de Mise en Page (Nouveau)
- **Détection automatique** des zones (texte, tableaux, titres, figures)
- **Mode fallback** si LayoutParser non installé
- **Export structuré** JSON + Word avec préservation de la structure
- **Images de debug** avec zones annotées

## 🚀 Utilisation

### Lancement Standard
```bash
# Double-clic ou ligne de commande
Lancer_OCR_Intelligent.bat
```

### Installation Détection de Mise en Page
```bash
# Installation avec repository Safran
tools\install_layout_detection.bat

# Test
tools\test_simple.py
```

### Interface
1. Cocher "[BETA] Activer la détection automatique de mise en page"
2. Téléverser image/PDF
3. Analyser automatiquement
4. Télécharger résultats Word/JSON

## 🔧 Configuration

### Repository Safran
Les dépendances de détection de mise en page utilisent :
```
--index-url https://artifacts.cloud.safran/repository/pypi-group/simple 
--trusted-host artifacts.cloud.safran
```

### Mode Fallback
Si LayoutParser n'est pas installé :
- ✅ Fonctionnement normal garanti
- ✅ Zone unique créée automatiquement
- ✅ OCR standard appliqué
- ✅ Structure JSON maintenue

## 📊 Résultats Générés

### Mode Standard
- `output/result_[method].docx` - Document Word
- Texte extrait avec confiance

### Mode Détection de Mise en Page
- `output/structured_result_[method].docx` - Word structuré
- `output/structured_result_[image].json` - JSON détaillé
- `output/debug/[image]_layout_debug.jpg` - Image annotée

## 🧹 Nettoyage Automatique

- **Dossier output** : Vidé à chaque lancement
- **Cache Python** : Ignoré par Git
- **Fichiers temporaires** : Supprimés automatiquement

## 📚 Documentation

- **README.md** : Guide principal
- **docs/LAYOUT_DETECTION_GUIDE.md** : Guide détaillé détection mise en page
- **docs/PROJECT_STRUCTURE.md** : Ce fichier
