#!/usr/bin/env python3
"""
Point d'entrée principal pour l'application OCR Intelligent
Compatible avec PyInstaller
"""
import os
import sys
from pathlib import Path

# Configuration des variables d'environnement
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"] = ""
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"

# Déterminer le chemin de base de l'application
if getattr(sys, 'frozen', False):
    # Si l'application est empaquetée avec PyInstaller
    base_path = Path(sys._MEIPASS)
else:
    # En développement
    base_path = Path(__file__).parent

# Ajouter les chemins nécessaires
sys.path.insert(0, str(base_path))

# Importer et lancer l'application Streamlit
try:
    # Importer l'application frontend
    from frontend.app import main
    
    # Lancer l'application
    if __name__ == "__main__":
        main()
except ImportError:
    # Fallback si le module n'est pas trouvé
    import streamlit as st
    
    st.set_page_config(
        page_title="OCR Intelligent",
        page_icon="📄",
        layout="wide"
    )
    
    st.title("OCR Intelligent")
    
    st.error("""
    ### Erreur de chargement de l'application
    
    Impossible de charger les modules nécessaires. Veuillez vérifier l'installation.
    
    **Solutions possibles :**
    - Réinstallez l'application
    - Contactez le support technique
    """)
except Exception as e:
    # Capture toute autre erreur
    import streamlit as st
    
    st.set_page_config(
        page_title="OCR Intelligent - Erreur",
        page_icon="⚠️",
        layout="wide"
    )
    
    st.title("OCR Intelligent - Erreur")
    
    st.error(f"""
    ### Erreur lors du démarrage
    
    Une erreur s'est produite lors du démarrage de l'application :
    
    ```
    {str(e)}
    ```
    
    **Solutions possibles :**
    - Redémarrez l'application
    - Vérifiez que tous les fichiers sont présents
    - Réinstallez l'application
    """)
