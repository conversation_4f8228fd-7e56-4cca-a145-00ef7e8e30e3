@echo off
title Installation Basique - Detection de Mise en Page
echo ============================================================
echo INSTALLATION BASIQUE - DETECTION DE MISE EN PAGE
echo ============================================================
echo.
echo [INFO] Installation minimale pour environnement Safran
echo [INFO] Mode fallback garanti meme sans detectron2
echo.

REM Verification de Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python n'est pas installe ou pas dans le PATH
    pause
    exit /b 1
)
echo [OK] Python detecte: 
python --version
echo.

echo [STEP 1/2] Installation OpenCV pour detection de mise en page...
pip install opencv-python --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [WARNING] Echec depuis repository Safran
    pip install opencv-python
    if errorlevel 1 (
        echo [INFO] OpenCV non installe - mode fallback simple uniquement
    )
)
echo.

echo [STEP 2/2] Installation dependances complementaires...
pip install scikit-image scipy --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [INFO] Installation depuis PyPI standard...
    pip install scikit-image scipy
    if errorlevel 1 (
        echo [INFO] Dependances optionnelles non installees
    )
)
echo.

echo [TEST] Verification...
python -c "import cv2; print('[OK] OpenCV disponible')" 2>nul || echo [INFO] Mode fallback simple uniquement
python -c "import numpy; print('[OK] NumPy disponible')" 2>nul || echo [ERROR] NumPy manquant
python -c "import PIL; print('[OK] Pillow disponible')" 2>nul || echo [ERROR] Pillow manquant

echo.
echo ============================================================
echo [INFO] Installation basique terminee - Detection OpenCV
echo.
echo NOUVELLE APPROCHE:
echo - Detection de mise en page basee sur OpenCV
echo - Pas de dependance LayoutParser ou detectron2
echo - Analyse de contours et structures d'image
echo - Compatible environnement Safran
echo.
echo Test: python tools\test_simple.py
echo Lancement: Lancer_OCR_Intelligent.bat
echo ============================================================
pause
