@echo off
title Installation Basique - Detection de Mise en Page
echo ============================================================
echo INSTALLATION BASIQUE - DETECTION DE MISE EN PAGE
echo ============================================================
echo.
echo [INFO] Installation minimale pour environnement Safran
echo [INFO] Mode fallback garanti meme sans detectron2
echo.

REM Verification de Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python n'est pas installe ou pas dans le PATH
    pause
    exit /b 1
)
echo [OK] Python detecte: 
python --version
echo.

echo [STEP 1/2] Installation LayoutParser basique...
pip install layoutparser --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [WARNING] Echec depuis repository Safran
    pip install layoutparser
    if errorlevel 1 (
        echo [INFO] LayoutParser non installe - mode fallback uniquement
    )
)
echo.

echo [STEP 2/2] Installation PyTorch CPU...
pip install torch torchvision --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [INFO] Installation torch CPU depuis PyPI...
    pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
)
echo.

echo [TEST] Verification...
python -c "try: import layoutparser; print('[OK] LayoutParser disponible')" 2>nul || echo [INFO] Mode fallback uniquement
python -c "try: import torch; print('[OK] PyTorch disponible')" 2>nul || echo [WARNING] PyTorch manquant
python -c "try: import detectron2; print('[OK] Detectron2 disponible')" 2>nul || echo [INFO] Detectron2 non installe - mode fallback

echo.
echo ============================================================
echo [INFO] Installation basique terminee
echo.
echo IMPORTANT:
echo - Detectron2 non installe (optionnel)
echo - Mode fallback garanti
echo - Fonctionnement normal assure
echo.
echo Test: python tools\test_simple.py
echo Lancement: Lancer_OCR_Intelligent.bat
echo ============================================================
pause
