@echo off
echo ============================================================
echo INSTALLATION - DETECTION DE MISE EN PAGE
echo ============================================================
echo.

echo [INFO] Installation des dependances pour la detection de mise en page...
echo.

echo [STEP 1/3] Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python 3.8+ avant de continuer
    pause
    exit /b 1
)
python --version
echo [OK] Python detecte
echo.

echo [STEP 2/3] Installation de LayoutParser...
echo [INFO] Utilisation du repository Safran...
pip install layoutparser[paddledetection]>=0.3.4 --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [WARNING] Echec installation depuis repository Safran
    echo [INFO] Tentative installation basique...
    pip install layoutparser>=0.3.4 --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
)
echo.

echo [STEP 3/3] Installation de Detectron2...
echo [INFO] Installation version CPU-only depuis repository Safran...
pip install detectron2>=0.6 --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [WARNING] Echec installation Detectron2 depuis repository Safran
    echo [INFO] Tentative installation depuis PyPI standard...
    pip install detectron2>=0.6
)
echo.

echo [TEST] Verification de l'installation...
python -c "
try:
    import layoutparser as lp
    print('[OK] LayoutParser installe avec succes')
    try:
        model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')
        print('[OK] Modele PubLayNet accessible')
    except:
        print('[INFO] Modele PubLayNet sera telecharge lors de la premiere utilisation')
except ImportError as e:
    print('[WARNING] LayoutParser non disponible:', e)
    print('[INFO] Le mode fallback sera utilise')
"

echo.
echo ============================================================
echo [INFO] Installation terminee
echo.
echo Pour tester la detection de mise en page:
echo   python test_simple.py
echo.
echo Pour lancer l'application:
echo   python main.py
echo   ou
echo   Lancer_OCR_Intelligent.bat
echo.
echo Dans l'interface, cochez la case:
echo "[BETA] Activer la detection automatique de mise en page"
echo ============================================================
pause
