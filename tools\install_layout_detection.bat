@echo off
echo ============================================================
echo INSTALLATION - DETECTION DE MISE EN PAGE
echo ============================================================
echo.

echo [INFO] Installation des dependances pour la detection de mise en page...
echo.

echo [STEP 1/3] Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python 3.8+ avant de continuer
    pause
    exit /b 1
)
python --version
echo [OK] Python detecte
echo.

echo [STEP 2/4] Installation de LayoutParser...
echo [INFO] Utilisation du repository Safran...
pip install layoutparser --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [WARNING] Echec installation depuis repository Safran
    echo [INFO] Tentative installation depuis PyPI standard...
    pip install layoutparser
    if errorlevel 1 (
        echo [ERROR] Echec installation LayoutParser
        echo [INFO] Le mode fallback sera utilise
    )
)
echo.

echo [STEP 3/4] Installation de Detectron2 (optionnel)...
echo [INFO] Tentative installation depuis repository Safran...
pip install detectron2 --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [WARNING] Detectron2 non disponible depuis repository Safran
    echo [INFO] Tentative installation depuis PyPI standard...
    pip install detectron2
    if errorlevel 1 (
        echo [INFO] Detectron2 non installe - LayoutParser utilisera un mode degrade
        echo [INFO] Cela n'affecte pas le fonctionnement de base
    )
)
echo.

echo [STEP 4/4] Installation des dependances complementaires...
pip install torch torchvision --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [INFO] Installation torch depuis PyPI standard...
    pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
)
echo.

echo [TEST] Verification de l'installation...
python -c "import sys; print('Python:', sys.version.split()[0])"
python -c "import layoutparser; print('[OK] LayoutParser installe')" 2>nul || echo [WARNING] LayoutParser non disponible - mode fallback
python -c "import detectron2; print('[OK] Detectron2 installe')" 2>nul || echo [INFO] Detectron2 non disponible - mode degrade
python -c "import torch; print('[OK] PyTorch installe')" 2>nul || echo [WARNING] PyTorch non disponible

echo.
echo ============================================================
echo [INFO] Installation terminee
echo.
echo Pour tester la detection de mise en page:
echo   python tools\test_simple.py
echo.
echo Pour lancer l'application:
echo   python main.py
echo   ou
echo   Lancer_OCR_Intelligent.bat
echo.
echo Dans l'interface, cochez la case:
echo "[BETA] Activer la detection automatique de mise en page"
echo ============================================================
pause
