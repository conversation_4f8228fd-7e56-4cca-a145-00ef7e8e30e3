@echo off
echo ============================================================
echo INSTALLATION - DETECTION DE MISE EN PAGE
echo ============================================================
echo.

echo [INFO] Installation des dependances pour la detection de mise en page...
echo.

echo [STEP 1/3] Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python 3.8+ avant de continuer
    pause
    exit /b 1
)
python --version
echo [OK] Python detecte
echo.

echo [STEP 2/3] Installation des dependances de detection de mise en page...
echo [INFO] Utilisation du repository Safran...
pip install opencv-python scikit-image scipy --index-url https://artifacts.cloud.safran/repository/pypi-group/simple --trusted-host artifacts.cloud.safran
if errorlevel 1 (
    echo [WARNING] Echec installation depuis repository Safran
    echo [INFO] Tentative installation depuis PyPI standard...
    pip install opencv-python scikit-image scipy
    if errorlevel 1 (
        echo [ERROR] Echec installation des dependances OpenCV
        echo [INFO] Le mode fallback simple sera utilise
    )
)
echo.

echo [STEP 3/3] Verification des dependances existantes...
echo [INFO] Verification de NumPy et Pillow (deja installes)...
python -c "import numpy, PIL; print('[OK] NumPy et Pillow disponibles')" 2>nul || echo [WARNING] NumPy ou Pillow manquant
echo.

echo [TEST] Verification de l'installation...
python -c "import sys; print('Python:', sys.version.split()[0])"
python -c "import cv2; print('[OK] OpenCV installe')" 2>nul || echo [WARNING] OpenCV non disponible - mode fallback simple
python -c "import numpy; print('[OK] NumPy installe')" 2>nul || echo [ERROR] NumPy manquant
python -c "import PIL; print('[OK] Pillow installe')" 2>nul || echo [ERROR] Pillow manquant
python -c "import scipy, skimage; print('[OK] SciPy et scikit-image installes')" 2>nul || echo [INFO] SciPy/scikit-image optionnels

echo.
echo ============================================================
echo [INFO] Installation terminee - Detection de mise en page OpenCV
echo.
echo NOUVELLE APPROCHE:
echo - Utilise OpenCV pour l'analyse d'image
echo - Pas de dependance detectron2 ou LayoutParser
echo - Compatible environnement Safran
echo - Detection de titres, tableaux et zones de texte
echo.
echo Pour tester la detection de mise en page:
echo   python tools\test_simple.py
echo.
echo Pour lancer l'application:
echo   python main.py
echo   ou
echo   Lancer_OCR_Intelligent.bat
echo.
echo Dans l'interface, cochez la case:
echo "[BETA] Activer la detection automatique de mise en page"
echo ============================================================
pause
