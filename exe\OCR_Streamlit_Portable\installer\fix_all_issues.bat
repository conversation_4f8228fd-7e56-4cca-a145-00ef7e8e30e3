@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================================================
echo RÉSOLUTION COMPLÈTE DES PROBLÈMES - OCR INTELLIGENT
echo ================================================================
echo.
echo Ce script va résoudre les problèmes identifiés :
echo 1. Dépendances Python manquantes
echo 2. Installation d'Inno Setup
echo.

:: Vérifier que Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERREUR] Python n'est pas installé ou n'est pas dans le PATH
    echo.
    echo Veuillez d'abord installer Python 3.8+ depuis https://python.org
    echo Puis redémarrez cette invite de commande.
    echo.
    pause
    exit /b 1
)

echo [INFO] Python détecté
python --version
echo.

:: Étape 1 : Corriger les dépendances Python
echo ================================================================
echo ÉTAPE 1/2 : CORRECTION DES DÉPENDANCES PYTHON
echo ================================================================
echo.

call fix_dependencies.bat
set DEPS_RESULT=!errorlevel!

if !DEPS_RESULT! neq 0 (
    echo.
    echo [ERROR] Échec de la correction des dépendances Python
    echo Veuillez résoudre les problèmes de dépendances avant de continuer.
    echo.
    pause
    exit /b 1
)

echo.
echo [OK] Dépendances Python corrigées avec succès
echo.

:: Étape 2 : Installer Inno Setup
echo ================================================================
echo ÉTAPE 2/2 : INSTALLATION D'INNO SETUP
echo ================================================================
echo.

call install_inno_setup.bat
set INNO_RESULT=!errorlevel!

if !INNO_RESULT! neq 0 (
    echo.
    echo [ERROR] Échec de l'installation d'Inno Setup
    echo Veuillez installer Inno Setup manuellement depuis https://jrsoftware.org/isinfo.php
    echo.
    pause
    exit /b 1
)

echo.
echo [OK] Inno Setup installé avec succès
echo.

:: Vérification finale
echo ================================================================
echo VÉRIFICATION FINALE
echo ================================================================
echo.

echo [STEP] Exécution de la vérification complète...
python check_ready.py
set CHECK_RESULT=!errorlevel!

echo.
echo ================================================================
if !CHECK_RESULT! equ 0 (
    echo RÉSOLUTION COMPLÈTE RÉUSSIE
    echo ================================================================
    echo.
    echo ✓ Toutes les dépendances Python sont installées
    echo ✓ Inno Setup est installé et fonctionnel
    echo ✓ L'environnement est prêt pour la création de l'installateur
    echo.
    echo Prochaines étapes :
    echo 1. Lancez create_installer.bat pour créer l'installateur
    echo 2. Testez l'installateur sur une machine propre
    echo 3. Distribuez l'installateur OCR Intelligent
    echo.
    
    set /p LAUNCH_BUILD="Lancer la création de l'installateur maintenant ? (o/N) : "
    if /i "!LAUNCH_BUILD!"=="o" (
        echo.
        echo [INFO] Lancement de la création de l'installateur...
        call create_installer.bat
    )
    
) else (
    echo RÉSOLUTION PARTIELLE
    echo ================================================================
    echo.
    echo ⚠ Certains problèmes persistent
    echo.
    echo Actions recommandées :
    echo 1. Consultez les messages d'erreur ci-dessus
    echo 2. Corrigez manuellement les problèmes restants
    echo 3. Relancez check_ready.py pour vérifier
    echo 4. Contactez le support technique si nécessaire
    echo.
)

echo Appuyez sur une touche pour fermer...
pause >nul
exit /b !CHECK_RESULT!
