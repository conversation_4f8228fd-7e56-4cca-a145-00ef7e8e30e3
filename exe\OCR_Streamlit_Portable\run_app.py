#!/usr/bin/env python3
"""
Script de lancement de l'application OCR Streamlit
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """Lance l'application Streamlit"""
    print("="*60)
    print("LANCEMENT APPLICATION OCR")
    print("="*60)
    
    # Vérifier que app.py existe
    app_path = Path("app.py")
    if not app_path.exists():
        print(f"[ERROR] Fichier app.py manquant: {app_path}")
        return False
    
    print("[INFO] Lancement de l'application Streamlit...")
    print("[INFO] L'application sera accessible sur http://localhost:8501")
    print("[INFO] Appuyez sur Ctrl+C pour arrêter l'application")
    print("="*60)
    
    try:
        # Lancer Streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.headless", "true"
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Erreur lors du lancement de Streamlit: {e}")
        return False
    except KeyboardInterrupt:
        print("\n[INFO] Application arrêtée par l'utilisateur")
        return True
    except Exception as e:
        print(f"[ERROR] Erreur inattendue: {e}")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nApplication interrompue par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        sys.exit(1)
