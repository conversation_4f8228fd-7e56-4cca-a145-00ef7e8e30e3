"""
Module de préprocessing d'images pour l'OCR
Fournit différentes méthodes d'optimisation selon le moteur OCR utilisé
"""
import cv2
import numpy as np

def preprocess_image(path, method="enhanced"):
    """
    Préprocessing d'image avec différentes méthodes optimisées

    Args:
        path (str): Chemin vers l'image à traiter
        method (str): Méthode de préprocessing
            - "basic": Conversion simple en niveaux de gris
            - "enhanced": Préprocessing avancé avec correction d'inclinaison
            - "tesseract_optimized": Optimisé spécifiquement pour Tesseract
            - "table_optimized": Optimisé pour la reconnaissance de tableaux
            - "title_optimized": Optimisé pour les titres et en-têtes

    Returns:
        numpy.ndarray: Image préprocessée
    """
    image = cv2.imread(path)
    if image is None:
        raise FileNotFoundError(f"Image introuvable : {path}")

    if method == "basic":
        # Conversion simple en niveaux de gris
        return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    elif method == "tesseract_optimized":
        # Préprocessing optimisé pour Tesseract
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Améliorer le contraste
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        gray = clahe.apply(gray)

        # Débruitage
        gray = cv2.bilateralFilter(gray, 9, 75, 75)

        # Binarisation adaptative améliorée
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 15, 10)

        # Morphologie pour nettoyer
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        # Redimensionner si l'image est trop petite
        height, width = binary.shape
        if height < 300 or width < 300:
            scale_factor = max(300/height, 300/width)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            binary = cv2.resize(binary, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        return binary

    elif method == "table_optimized":
        # Préprocessing spécialisé pour les tableaux
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Améliorer le contraste pour faire ressortir les lignes
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        gray = clahe.apply(gray)

        # Débruitage léger pour préserver les lignes fines
        gray = cv2.medianBlur(gray, 3)

        # Binarisation pour faire ressortir les structures
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 11, 2)

        # Morphologie pour renforcer les lignes horizontales et verticales
        # Lignes horizontales
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
        horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)

        # Lignes verticales
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))
        vertical_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)

        # Combiner les lignes avec l'image originale
        table_structure = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)
        enhanced_binary = cv2.addWeighted(binary, 0.8, table_structure, 0.2, 0.0)

        return enhanced_binary

    elif method == "title_optimized":
        # Préprocessing spécialisé pour les titres et en-têtes
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Contraste élevé pour les gros textes
        clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(4,4))
        gray = clahe.apply(gray)

        # Débruitage plus agressif car les titres sont généralement plus gros
        gray = cv2.bilateralFilter(gray, 15, 80, 80)

        # Binarisation avec seuil plus strict
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 21, 8)

        # Morphologie pour épaissir légèrement le texte
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        return binary

    else:  # method == "enhanced" (défaut)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Correction de l'inclinaison (deskewing)
        coords = cv2.findNonZero(cv2.bitwise_not(gray))
        if coords is not None:
            angle = cv2.minAreaRect(coords)[-1]
            angle = -(90 + angle) if angle < -45 else -angle
            (h, w) = gray.shape[:2]
            M = cv2.getRotationMatrix2D((w // 2, h // 2), angle, 1.0)
            gray = cv2.warpAffine(gray, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)

        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 11, 2)

        # Débruitage
        binary = cv2.medianBlur(binary, 3)

        # Morphologie
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        return cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)