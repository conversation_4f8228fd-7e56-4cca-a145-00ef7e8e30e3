#!/usr/bin/env python3
"""
Script pour diagnostiquer et corriger les dépendances Python
"""
import os
import sys
import subprocess
import importlib
from pathlib import Path

def print_header(title):
    """Affiche un en-tête"""
    print(f"\n{'='*60}")
    print(f"{title}")
    print(f"{'='*60}")

def print_step(message):
    """Affiche une étape"""
    print(f"\n[STEP] {message}")

def print_info(message):
    """Affiche une information"""
    print(f"[INFO] {message}")

def print_success(message):
    """Affiche un succès"""
    print(f"[OK] {message}")

def print_error(message):
    """Affiche une erreur"""
    print(f"[ERROR] {message}")

def print_warning(message):
    """Affiche un avertissement"""
    print(f"[WARNING] {message}")

def check_python_version():
    """Vérifie la version de Python"""
    print_step("Vérification de la version Python")
    
    version = sys.version_info
    print_info(f"Version Python détectée: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print_success("Version Python compatible")
        return True
    else:
        print_error("Python 3.8+ requis")
        return False

def get_package_version(package_name):
    """Obtient la version d'un package installé"""
    try:
        if package_name == "PIL":
            import PIL
            return PIL.__version__
        elif package_name == "cv2":
            import cv2
            return cv2.__version__
        else:
            module = importlib.import_module(package_name)
            return getattr(module, '__version__', 'Version inconnue')
    except ImportError:
        return None
    except Exception as e:
        return f"Erreur: {e}"

def install_package(package_name, pip_name=None):
    """Installe un package Python"""
    if pip_name is None:
        pip_name = package_name
    
    print_info(f"Installation de {package_name}...")
    
    try:
        # Utiliser --user pour éviter les problèmes de permissions
        cmd = [sys.executable, "-m", "pip", "install", "--user", "--upgrade", pip_name]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print_success(f"{package_name} installé avec succès")
            return True
        else:
            print_error(f"Erreur lors de l'installation de {package_name}")
            print_error(f"Sortie: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print_error(f"Timeout lors de l'installation de {package_name}")
        return False
    except Exception as e:
        print_error(f"Erreur inattendue lors de l'installation de {package_name}: {e}")
        return False

def fix_dependencies():
    """Corrige les dépendances manquantes"""
    print_step("Diagnostic et correction des dépendances")
    
    # Liste des dépendances avec leurs noms pip si différents
    dependencies = {
        "streamlit": "streamlit",
        "PIL": "Pillow",
        "numpy": "numpy",
        "pandas": "pandas", 
        "pytesseract": "pytesseract",
        "easyocr": "easyocr",
        "doctr": "python-doctr",
        "PyInstaller": "pyinstaller",
        "cv2": "opencv-python",
        "torch": "torch",
        "torchvision": "torchvision",
        "fitz": "PyMuPDF",
        "docx": "python-docx",
        "openpyxl": "openpyxl",
        "reportlab": "reportlab"
    }
    
    missing_packages = []
    installed_packages = []
    
    # Vérifier chaque dépendance
    for import_name, pip_name in dependencies.items():
        version = get_package_version(import_name)
        
        if version is None:
            print_error(f"{import_name} - Non installé")
            missing_packages.append((import_name, pip_name))
        elif version.startswith("Erreur"):
            print_warning(f"{import_name} - {version}")
            missing_packages.append((import_name, pip_name))
        else:
            print_success(f"{import_name} - Version {version}")
            installed_packages.append((import_name, version))
    
    print_info(f"Packages installés: {len(installed_packages)}")
    print_info(f"Packages manquants: {len(missing_packages)}")
    
    # Installer les packages manquants
    if missing_packages:
        print_step("Installation des packages manquants")
        
        failed_installations = []
        
        for import_name, pip_name in missing_packages:
            if not install_package(import_name, pip_name):
                failed_installations.append((import_name, pip_name))
        
        if failed_installations:
            print_error("Échec d'installation pour:")
            for import_name, pip_name in failed_installations:
                print_error(f"  - {import_name} ({pip_name})")
            return False
        else:
            print_success("Toutes les dépendances ont été installées")
            return True
    else:
        print_success("Toutes les dépendances sont déjà installées")
        return True

def install_from_requirements():
    """Installe les dépendances depuis requirements.txt"""
    print_step("Installation depuis requirements.txt")
    
    requirements_path = Path(__file__).parent.parent / "requirements.txt"
    
    if not requirements_path.exists():
        print_warning("Fichier requirements.txt non trouvé")
        return True
    
    try:
        cmd = [sys.executable, "-m", "pip", "install", "--user", "-r", str(requirements_path)]
        
        print_info(f"Commande: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print_success("Installation depuis requirements.txt réussie")
            return True
        else:
            print_error("Erreur lors de l'installation depuis requirements.txt")
            print_error(f"Sortie: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print_error("Timeout lors de l'installation depuis requirements.txt")
        return False
    except Exception as e:
        print_error(f"Erreur inattendue: {e}")
        return False

def verify_critical_imports():
    """Vérifie que les imports critiques fonctionnent"""
    print_step("Vérification des imports critiques")
    
    critical_imports = [
        ("streamlit", "import streamlit as st"),
        ("PIL", "from PIL import Image"),
        ("numpy", "import numpy as np"),
        ("pytesseract", "import pytesseract"),
        ("easyocr", "import easyocr"),
        ("PyInstaller", "import PyInstaller")
    ]
    
    all_ok = True
    
    for name, import_statement in critical_imports:
        try:
            exec(import_statement)
            print_success(f"{name} - Import réussi")
        except ImportError as e:
            print_error(f"{name} - Échec import: {e}")
            all_ok = False
        except Exception as e:
            print_warning(f"{name} - Avertissement: {e}")
    
    return all_ok

def main():
    """Fonction principale"""
    print_header("CORRECTION DES DÉPENDANCES PYTHON")
    
    # Vérifier la version Python
    if not check_python_version():
        print_error("Version Python incompatible")
        return 1
    
    # Mettre à jour pip
    print_step("Mise à jour de pip")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--user", "--upgrade", "pip"], 
                      capture_output=True, timeout=120)
        print_success("pip mis à jour")
    except:
        print_warning("Impossible de mettre à jour pip")
    
    # Installer depuis requirements.txt
    req_success = install_from_requirements()
    
    # Corriger les dépendances manquantes
    deps_success = fix_dependencies()
    
    # Vérifier les imports critiques
    imports_success = verify_critical_imports()
    
    # Résumé
    print_header("RÉSUMÉ")
    
    if req_success and deps_success and imports_success:
        print_success("✓ Toutes les dépendances sont correctement installées")
        print_info("Vous pouvez maintenant procéder à l'installation d'Inno Setup")
        return 0
    else:
        print_error("✗ Certaines dépendances ont des problèmes")
        print_info("Solutions possibles:")
        print_info("1. Exécutez ce script en tant qu'administrateur")
        print_info("2. Utilisez un environnement virtuel Python")
        print_info("3. Installez manuellement les packages manquants")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nCorrection interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        sys.exit(1)
