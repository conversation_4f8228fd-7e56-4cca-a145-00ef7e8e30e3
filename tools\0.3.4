Looking in indexes: https://artifacts.cloud.safran/repository/pypi-group/simple
Requirement already satisfied: layoutparser[paddledetection] in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (0.2.0)
Requirement already satisfied: numpy in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (2.3.1)
Requirement already satisfied: opencv-python in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (4.11.0.86)
Requirement already satisfied: pandas in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (2.3.1)
Requirement already satisfied: pillow in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (10.3.0)
Requirement already satisfied: pyyaml>=5.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (6.0.2)
Requirement already satisfied: torch in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (2.7.0)
Requirement already satisfied: torchvision in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (0.22.0)
Requirement already satisfied: iopath in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (0.1.10)
Requirement already satisfied: tqdm in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from iopath->layoutparser[paddledetection]) (4.67.1)
Requirement already satisfied: typing-extensions in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from iopath->layoutparser[paddledetection]) (4.13.2)
Requirement already satisfied: portalocker in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from iopath->layoutparser[paddledetection]) (3.2.0)
Requirement already satisfied: python-dateutil>=2.8.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pandas->layoutparser[paddledetection]) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pandas->layoutparser[paddledetection]) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pandas->layoutparser[paddledetection]) (2025.2)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from python-dateutil>=2.8.2->pandas->layoutparser[paddledetection]) (1.17.0)
Requirement already satisfied: pywin32>=226 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from portalocker->iopath->layoutparser[paddledetection]) (310)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (3.18.0)
Requirement already satisfied: sympy>=1.13.3 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (1.14.0)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (3.1)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (3.1.6)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (2025.3.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from sympy>=1.13.3->torch->layoutparser[paddledetection]) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from jinja2->torch->layoutparser[paddledetection]) (3.0.2)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tqdm->iopath->layoutparser[paddledetection]) (0.4.6)
