#!/usr/bin/env python3
"""
Script de vérification avant construction de l'installateur
"""
import os
import sys
import subprocess
from pathlib import Path

def print_header(title):
    """Affiche un en-tête"""
    print(f"\n{'='*60}")
    print(f"{title}")
    print(f"{'='*60}")

def print_check(item, status, details=""):
    """Affiche le résultat d'une vérification"""
    symbol = "✓" if status else "✗"
    print(f"{symbol} {item}")
    if details:
        print(f"  {details}")

def check_python():
    """Vérifie Python"""
    try:
        version = sys.version.split()[0]
        major, minor = map(int, version.split('.')[:2])
        
        if major >= 3 and minor >= 8:
            print_check(f"Python {version}", True, "Version compatible")
            return True
        else:
            print_check(f"Python {version}", False, "Version 3.8+ requise")
            return False
    except Exception as e:
        print_check("Python", False, f"Erreur: {e}")
        return False

def check_dependencies():
    """Vérifie les dépendances Python"""
    deps = {
        "streamlit": "Interface web",
        "PIL": "Traitement d'images",
        "numpy": "Calculs numériques",
        "pandas": "Manipulation de données",
        "pytesseract": "OCR Tesseract",
        "easyocr": "OCR EasyOCR",
        "doctr": "OCR DocTR",
        "PyInstaller": "Empaquetage"
    }
    
    all_ok = True
    for dep, desc in deps.items():
        try:
            if dep == "PIL":
                import PIL
            else:
                __import__(dep)
            print_check(f"{dep}", True, desc)
        except ImportError:
            print_check(f"{dep}", False, f"{desc} - Non installé")
            all_ok = False
    
    return all_ok

def check_application_files():
    """Vérifie les fichiers de l'application"""
    base_dir = Path(__file__).parent.parent
    
    required_files = [
        ("app.py", "Point d'entrée principal"),
        ("frontend/app.py", "Interface Streamlit"),
        ("backend/main.py", "Logique métier"),
        ("backend/ocr_tesseract.py", "Module Tesseract"),
        ("backend/ocr_easyocr.py", "Module EasyOCR"),
        ("backend/ocr_doctr.py", "Module DocTR"),
        ("requirements.txt", "Liste des dépendances")
    ]
    
    all_ok = True
    for file_path, desc in required_files:
        full_path = base_dir / file_path
        if full_path.exists():
            print_check(file_path, True, desc)
        else:
            print_check(file_path, False, f"{desc} - Fichier manquant")
            all_ok = False
    
    return all_ok

def check_models():
    """Vérifie les modèles OCR"""
    base_dir = Path(__file__).parent.parent
    models_dir = base_dir / "models"
    
    if not models_dir.exists():
        print_check("Dossier models/", False, "Dossier manquant")
        return False
    
    # Vérifier les modèles DocTR
    doctr_models = [
        "doctr/db_mobilenet_v3_large/db_mobilenet_v3_large-21748dd0.pt",
        "doctr/crnn_vgg16_bn/crnn_vgg16_bn-9762b0b0.pt"
    ]
    
    # Vérifier les modèles EasyOCR
    easyocr_models = [
        "easyocr/craft_mlt_25k.pth",
        "easyocr/latin_g2.pth",
        "easyocr/english_g2.pth"
    ]
    
    # Vérifier Tesseract
    tesseract_models = [
        "tesseract/tessdata"
    ]
    
    all_models = [
        ("DocTR", doctr_models),
        ("EasyOCR", easyocr_models),
        ("Tesseract", tesseract_models)
    ]
    
    all_ok = True
    for engine, model_list in all_models:
        engine_ok = True
        for model_path in model_list:
            full_path = models_dir / model_path
            if full_path.exists():
                if full_path.is_file():
                    size_mb = full_path.stat().st_size / (1024 * 1024)
                    print_check(f"{engine} - {model_path}", True, f"{size_mb:.1f} MB")
                else:
                    print_check(f"{engine} - {model_path}", True, "Dossier")
            else:
                print_check(f"{engine} - {model_path}", False, "Manquant")
                engine_ok = False
        
        if not engine_ok:
            all_ok = False
    
    return all_ok

def check_installer_files():
    """Vérifie les fichiers de l'installateur"""
    installer_dir = Path(__file__).parent
    
    required_files = [
        ("build_installer.py", "Script de construction"),
        ("create_icon.py", "Générateur d'icône"),
        ("ocr_setup.iss", "Script Inno Setup"),
        ("create_installer.bat", "Script batch"),
        ("documentation/Guide_Utilisateur.md", "Documentation")
    ]
    
    all_ok = True
    for file_path, desc in required_files:
        full_path = installer_dir / file_path
        if full_path.exists():
            print_check(file_path, True, desc)
        else:
            print_check(file_path, False, f"{desc} - Fichier manquant")
            all_ok = False
    
    return all_ok

def check_inno_setup():
    """Vérifie Inno Setup"""
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]
    
    for path in inno_paths:
        if Path(path).exists():
            print_check("Inno Setup", True, f"Trouvé: {path}")
            return True
    
    print_check("Inno Setup", False, "Non installé - Téléchargez depuis https://jrsoftware.org/isinfo.php")
    return False

def estimate_size():
    """Estime la taille de l'installateur"""
    base_dir = Path(__file__).parent.parent
    
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            file_path = Path(root) / file
            try:
                total_size += file_path.stat().st_size
                file_count += 1
            except:
                pass
    
    size_mb = total_size / (1024 * 1024)
    print_check("Taille estimée", True, f"{size_mb:.1f} MB ({file_count} fichiers)")
    
    if size_mb > 1000:
        print("  ⚠️  Taille importante - Considérez optimiser les modèles")
    
    return True

def main():
    """Fonction principale"""
    print_header("VÉRIFICATION AVANT CONSTRUCTION")
    
    checks = [
        ("Python", check_python),
        ("Dépendances Python", check_dependencies),
        ("Fichiers application", check_application_files),
        ("Modèles OCR", check_models),
        ("Fichiers installateur", check_installer_files),
        ("Inno Setup", check_inno_setup),
        ("Estimation taille", estimate_size)
    ]
    
    results = []
    
    for name, check_func in checks:
        print_header(f"VÉRIFICATION: {name}")
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print_check(name, False, f"Erreur: {e}")
            results.append((name, False))
    
    # Résumé
    print_header("RÉSUMÉ")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        print_check(name, result)
    
    print(f"\nRésultat: {passed}/{total} vérifications réussies")
    
    if passed == total:
        print("\n🎉 Tout est prêt pour la construction de l'installateur!")
        print("Exécutez 'create_installer.bat' pour commencer.")
    elif passed >= total - 1:
        print("\n⚠️  Presque prêt - Corrigez les problèmes mineurs et relancez.")
    else:
        print("\n❌ Plusieurs problèmes détectés - Corrigez-les avant de continuer.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nVérification interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        sys.exit(1)
