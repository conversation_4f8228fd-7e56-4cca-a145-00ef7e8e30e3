@echo off
title OCR Intelligent - Mode Detection de Mise en Page
color 0A

echo.
echo ============================================================
echo    OCR INTELLIGENT - MODE DETECTION DE MISE EN PAGE
echo ============================================================
echo.
echo [INFO] Lancement de l'application avec detection automatique
echo        de mise en page activee par defaut...
echo.

REM Verification de Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python n'est pas installe ou pas dans le PATH
    echo.
    echo Solutions:
    echo 1. Installez Python depuis https://python.org
    echo 2. Assurez-vous que Python est dans le PATH
    echo 3. Redemarrez votre ordinateur apres installation
    echo.
    pause
    exit /b 1
)

echo [OK] Python detecte
echo.

REM Verification des dependances de base
echo [CHECK] Verification des dependances de base...
python -c "import streamlit, cv2, PIL, numpy" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Dependances de base manquantes
    echo [INFO] Installation automatique en cours...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Echec de l'installation des dependances
        echo Veuillez executer manuellement: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo [OK] Dependances de base presentes
echo.

REM Verification de LayoutParser (optionnel)
echo [CHECK] Verification de LayoutParser...
python -c "import layoutparser" >nul 2>&1
if errorlevel 1 (
    echo [INFO] LayoutParser non detecte - mode fallback sera utilise
    echo [INFO] Pour installer LayoutParser: install_layout_detection.bat
    echo.
) else (
    echo [OK] LayoutParser detecte - detection complete disponible
    echo.
)

REM Test rapide du systeme
echo [TEST] Test rapide du systeme...
python -c "
import sys
sys.path.insert(0, '.')
try:
    from backend.layout_detector import create_layout_detector
    detector = create_layout_detector()
    if detector.is_available():
        print('[OK] Detection de mise en page: COMPLETE')
    else:
        print('[OK] Detection de mise en page: MODE FALLBACK')
except Exception as e:
    print('[WARNING] Erreur lors du test:', e)
"

echo.
echo [INFO] Demarrage de l'application...
echo.
echo ============================================================
echo  INSTRUCTIONS D'UTILISATION
echo ============================================================
echo.
echo 1. L'application va s'ouvrir dans votre navigateur
echo 2. La detection de mise en page sera ACTIVEE par defaut
echo 3. Telechargez votre image ou PDF
echo 4. L'analyse detectera automatiquement:
echo    - Zones de texte
echo    - Tableaux
echo    - Titres et en-tetes
echo    - Figures et images
echo 5. Resultats disponibles en Word et JSON structure
echo.
echo ============================================================
echo.

REM Lancement de l'application
python main.py

REM Gestion des erreurs de lancement
if errorlevel 1 (
    echo.
    echo [ERROR] Erreur lors du lancement de l'application
    echo.
    echo Solutions possibles:
    echo 1. Verifiez que le port 8501 n'est pas occupe
    echo 2. Redemarrez votre ordinateur
    echo 3. Executez: python main.py manuellement
    echo.
    echo Pour plus d'aide, consultez:
    echo - LAYOUT_DETECTION_GUIDE.md
    echo - README.md
    echo.
    pause
    exit /b 1
)

echo.
echo [INFO] Application fermee normalement
pause
