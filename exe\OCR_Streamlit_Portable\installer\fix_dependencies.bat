@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================================================
echo CORRECTION DES DÉPENDANCES PYTHON - OCR INTELLIGENT
echo ================================================================
echo.

:: Vérifier que Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERREUR] Python n'est pas installé ou n'est pas dans le PATH
    echo.
    echo Solutions :
    echo 1. Installez Python 3.8+ depuis https://python.org
    echo 2. Assurez-vous que Python est dans le PATH système
    echo 3. Redémarrez l'invite de commande après installation
    echo.
    pause
    exit /b 1
)

echo [INFO] Python détecté
python --version
echo.

:: Mettre à jour pip
echo [STEP] Mise à jour de pip...
python -m pip install --user --upgrade pip
if errorlevel 1 (
    echo [WARNING] Impossible de mettre à jour pip, continuation...
)
echo.

:: Installer les dépendances essentielles une par une
echo [STEP] Installation des dépendances essentielles...
echo.

:: Streamlit
echo [INFO] Installation de Streamlit...
python -m pip install --user --upgrade streamlit
if errorlevel 1 (
    echo [ERROR] Échec installation Streamlit
) else (
    echo [OK] Streamlit installé
)

:: Pillow (PIL)
echo [INFO] Installation de Pillow...
python -m pip install --user --upgrade Pillow
if errorlevel 1 (
    echo [ERROR] Échec installation Pillow
) else (
    echo [OK] Pillow installé
)

:: NumPy
echo [INFO] Installation de NumPy...
python -m pip install --user --upgrade numpy
if errorlevel 1 (
    echo [ERROR] Échec installation NumPy
) else (
    echo [OK] NumPy installé
)

:: Pandas
echo [INFO] Installation de Pandas...
python -m pip install --user --upgrade pandas
if errorlevel 1 (
    echo [ERROR] Échec installation Pandas
) else (
    echo [OK] Pandas installé
)

:: PyTesseract
echo [INFO] Installation de PyTesseract...
python -m pip install --user --upgrade pytesseract
if errorlevel 1 (
    echo [ERROR] Échec installation PyTesseract
) else (
    echo [OK] PyTesseract installé
)

:: EasyOCR
echo [INFO] Installation de EasyOCR...
python -m pip install --user --upgrade easyocr
if errorlevel 1 (
    echo [ERROR] Échec installation EasyOCR
) else (
    echo [OK] EasyOCR installé
)

:: DocTR
echo [INFO] Installation de DocTR...
python -m pip install --user --upgrade python-doctr
if errorlevel 1 (
    echo [ERROR] Échec installation DocTR
) else (
    echo [OK] DocTR installé
)

:: PyInstaller
echo [INFO] Installation de PyInstaller...
python -m pip install --user --upgrade pyinstaller
if errorlevel 1 (
    echo [ERROR] Échec installation PyInstaller
) else (
    echo [OK] PyInstaller installé
)

:: Dépendances supplémentaires
echo [INFO] Installation des dépendances supplémentaires...
python -m pip install --user --upgrade opencv-python torch torchvision PyMuPDF python-docx openpyxl reportlab
if errorlevel 1 (
    echo [WARNING] Certaines dépendances supplémentaires ont échoué
) else (
    echo [OK] Dépendances supplémentaires installées
)

echo.
echo [STEP] Installation depuis requirements.txt...
if exist "..\requirements.txt" (
    python -m pip install --user -r "..\requirements.txt"
    if errorlevel 1 (
        echo [WARNING] Certains packages de requirements.txt ont échoué
    ) else (
        echo [OK] Requirements.txt traité
    )
) else (
    echo [INFO] Fichier requirements.txt non trouvé, ignoré
)

echo.
echo [STEP] Exécution du script de diagnostic...
python fix_dependencies.py
set DIAG_RESULT=!errorlevel!

echo.
echo ================================================================
if !DIAG_RESULT! equ 0 (
    echo CORRECTION RÉUSSIE
    echo ================================================================
    echo.
    echo ✓ Toutes les dépendances Python sont installées
    echo ✓ Les imports critiques fonctionnent
    echo.
    echo Prochaine étape : Installer Inno Setup
    echo 1. Téléchargez Inno Setup 6 depuis https://jrsoftware.org/isinfo.php
    echo 2. Installez-le avec les options par défaut
    echo 3. Relancez check_ready.py pour vérifier
    echo.
) else (
    echo CORRECTION PARTIELLE
    echo ================================================================
    echo.
    echo ⚠ Certaines dépendances ont encore des problèmes
    echo.
    echo Solutions possibles :
    echo 1. Exécutez ce script en tant qu'administrateur
    echo 2. Utilisez un environnement virtuel Python :
    echo    python -m venv venv
    echo    venv\Scripts\activate
    echo    pip install -r requirements.txt
    echo 3. Installez manuellement les packages manquants
    echo.
)

echo Appuyez sur une touche pour fermer...
pause >nul
exit /b !DIAG_RESULT!
