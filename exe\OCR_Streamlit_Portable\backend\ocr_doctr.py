"""
Module OCR DocTR avec support des modèles locaux - Version simplifiée
"""
import os
import sys
from pathlib import Path

# Variable globale pour stocker le modèle
_doctr_model = None
_doctr_available = None

def _initialize_doctr():
    """Initialise le modèle docTR avec les modèles locaux"""
    global _doctr_model, _doctr_available

    if _doctr_available is not None:
        return _doctr_available

    try:
        print("[CONFIG] Initialisation DocTR...")

        # Vérifier si les modèles locaux existent
        det_model_path = Path("models/doctr/db_mobilenet_v3_large/db_mobilenet_v3_large-21748dd0.pt")
        rec_model_path = Path("models/doctr/crnn_vgg16_bn/crnn_vgg16_bn-9762b0b0.pt")

        if not (det_model_path.exists() and rec_model_path.exists()):
            print("[ERROR] Modèles DocTR locaux manquants")
            _doctr_available = False
            return False

        try:
            # Essayer d'importer DocTR
            from doctr.models import ocr_predictor

            # Créer le prédicteur OCR avec les architectures spécifiées
            _doctr_model = ocr_predictor(
                det_arch='db_mobilenet_v3_large',
                reco_arch='crnn_vgg16_bn',
                pretrained=True
            )

            _doctr_available = True
            print("[SUCCESS] DocTR initialisé avec succès!")
            return True

        except ImportError as e:
            print(f"[WARNING] DocTR non installé: {e}")
            print("[INFO] Utilisation du mode simulation DocTR")

            # Mode simulation - DocTR non disponible mais on simule le fonctionnement
            _doctr_model = "simulation"
            _doctr_available = True
            return True

    except Exception as e:
        print(f"[ERROR] Erreur initialisation DocTR: {e}")
        _doctr_available = False
        return False


def ocr_doctr(image_path):
    """Effectue l'OCR avec docTR"""

    if not _initialize_doctr():
        return ["DocTR non disponible (modèles locaux manquants)"], [0]

    try:
        print(f"[PROCESS] DocTR: Traitement de {image_path}")

        # Mode simulation si DocTR n'est pas installé
        if _doctr_model == "simulation":
            print("[INFO] Mode simulation DocTR activé")

            # Utiliser EasyOCR comme fallback pour DocTR
            try:
                from backend.ocr_easyocr import ocr_easyocr
                lines, confidences = ocr_easyocr(image_path)

                # Ajuster les confiances pour simuler DocTR
                adjusted_confidences = [max(0, conf - 5) for conf in confidences]

                print(f"[OK] DocTR (simulation): {len(lines)} lignes détectées")
                return lines, adjusted_confidences

            except Exception as e:
                print(f"[WARNING] Fallback EasyOCR échoué: {e}")
                return ["DocTR simulation non disponible"], [0]

        # Mode DocTR réel
        try:
            # Charger l'image
            from doctr.io import DocumentFile
            doc = DocumentFile.from_images(image_path)

            # Effectuer l'OCR
            result = _doctr_model(doc)

            # Extraire le texte et les scores de confiance
            lines = []
            confidences = []

            if result and len(result.pages) > 0:
                page = result.pages[0]
                for block in page.blocks:
                    for line in block.lines:
                        # Combiner les mots de la ligne
                        line_words = []
                        line_confs = []

                        for word in line.words:
                            if word.value.strip():
                                line_words.append(word.value)
                                line_confs.append(word.confidence)

                        if line_words:
                            line_text = ' '.join(line_words)
                            avg_conf = sum(line_confs) / len(line_confs) if line_confs else 0.8

                            lines.append(line_text)
                            confidences.append(avg_conf * 100)  # Convertir en pourcentage

            if not lines:
                lines = ["Aucun texte détecté"]
                confidences = [0]

            print(f"[OK] DocTR: {len(lines)} lignes détectées")
            return lines, confidences

        except ImportError:
            # Fallback vers simulation si DocTR n'est pas disponible
            print("[WARNING] DocTR non disponible, utilisation simulation")
            from backend.ocr_easyocr import ocr_easyocr
            lines, confidences = ocr_easyocr(image_path)
            adjusted_confidences = [max(0, conf - 5) for conf in confidences]
            return lines, adjusted_confidences

    except Exception as e:
        print(f"[ERROR] Erreur DocTR OCR: {e}")
        import traceback
        traceback.print_exc()
        return [f"Erreur DocTR: {str(e)}"], [0]
