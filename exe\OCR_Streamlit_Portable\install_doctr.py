#!/usr/bin/env python3
"""
Script d'installation de DocTR à partir du wheel local
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header(message):
    """Affiche un en-tête"""
    print(f"\n{'='*60}")
    print(f"{message}")
    print(f"{'='*60}")

def print_step(message):
    """Affiche une étape"""
    print(f"[STEP] {message}")

def print_info(message):
    """Affiche une information"""
    print(f"[INFO] {message}")

def print_success(message):
    """Affiche un succès"""
    print(f"[OK] {message}")

def print_error(message):
    """Affiche une erreur"""
    print(f"[ERROR] {message}")

def print_warning(message):
    """Affiche un avertissement"""
    print(f"[WARNING] {message}")

def install_doctr():
    """Installe DocTR à partir du wheel local"""
    print_header("INSTALLATION DOCTR")
    
    # Vérifier si le wheel existe
    wheel_path = Path("models/python_doctr-1.0.0-py3-none-any.whl")
    if not wheel_path.exists():
        print_error(f"Wheel DocTR manquant: {wheel_path}")
        return False
    
    print_step(f"Installation de DocTR depuis {wheel_path}")
    
    try:
        # Installer le wheel
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", str(wheel_path), "--force-reinstall"],
            capture_output=True,
            text=True,
            check=True
        )
        
        print_info(result.stdout)
        print_success("DocTR installé avec succès")
        return True
        
    except subprocess.CalledProcessError as e:
        print_error(f"Erreur installation DocTR: {e}")
        print_error(f"Sortie: {e.stdout}")
        print_error(f"Erreur: {e.stderr}")
        return False
    except Exception as e:
        print_error(f"Erreur inattendue: {e}")
        return False

def fix_doctr_module():
    """Corrige le module DocTR"""
    print_header("CORRECTION MODULE DOCTR")
    
    module_path = Path("backend/ocr_doctr.py")
    if not module_path.exists():
        print_error(f"Module DocTR manquant: {module_path}")
        return False
    
    # Contenu de remplacement
    new_content = """\"\"\"
Module OCR DocTR avec support des modèles locaux - Version simplifiée
\"\"\"
import os
import sys
from pathlib import Path

# Variable globale pour stocker le modèle
_doctr_model = None
_doctr_available = None

def _initialize_doctr():
    \"\"\"Initialise le modèle docTR avec les modèles locaux\"\"\"
    global _doctr_model, _doctr_available
    
    if _doctr_available is not None:
        return _doctr_available
    
    try:
        print("[CONFIG] Initialisation DocTR...")
        
        try:
            # Importer DocTR
            from doctr.models import ocr_predictor
            
            # Créer le prédicteur OCR avec les architectures spécifiées
            _doctr_model = ocr_predictor(
                det_arch='db_mobilenet_v3_large',
                reco_arch='crnn_vgg16_bn',
                pretrained=True
            )
            
            _doctr_available = True
            print("[SUCCESS] DocTR initialisé avec succès!")
            return True
                
        except ImportError as e:
            print(f"[ERROR] DocTR non installé: {e}")
            _doctr_available = False
            return False

    except Exception as e:
        print(f"[ERROR] Erreur initialisation DocTR: {e}")
        _doctr_available = False
        return False


def ocr_doctr(image_path):
    \"\"\"Effectue l'OCR avec docTR\"\"\"
    
    if not _initialize_doctr():
        return ["DocTR non disponible"], [0]
    
    try:
        print(f"[PROCESS] DocTR: Traitement de {image_path}")
        
        # Charger l'image
        from doctr.io import DocumentFile
        doc = DocumentFile.from_images(image_path)
        
        # Effectuer l'OCR
        result = _doctr_model(doc)
        
        # Extraire le texte et les scores de confiance
        lines = []
        confidences = []
        
        if result and len(result.pages) > 0:
            page = result.pages[0]
            for block in page.blocks:
                for line in block.lines:
                    # Combiner les mots de la ligne
                    line_words = []
                    line_confs = []
                    
                    for word in line.words:
                        if word.value.strip():
                            line_words.append(word.value)
                            line_confs.append(word.confidence)
                    
                    if line_words:
                        line_text = ' '.join(line_words)
                        avg_conf = sum(line_confs) / len(line_confs) if line_confs else 0.8
                        
                        lines.append(line_text)
                        confidences.append(avg_conf * 100)  # Convertir en pourcentage
        
        if not lines:
            lines = ["Aucun texte détecté"]
            confidences = [0]
        
        print(f"[OK] DocTR: {len(lines)} lignes détectées")
        return lines, confidences
        
    except Exception as e:
        print(f"[ERROR] Erreur DocTR OCR: {e}")
        import traceback
        traceback.print_exc()
        return [f"Erreur DocTR: {str(e)}"], [0]
"""
    
    # Sauvegarder le fichier
    try:
        # Créer une sauvegarde
        backup_path = module_path.with_suffix(".py.bak")
        shutil.copy2(module_path, backup_path)
        print_info(f"Sauvegarde créée: {backup_path}")
        
        # Écrire le nouveau contenu
        with open(module_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print_success(f"Module DocTR corrigé: {module_path}")
        return True
        
    except Exception as e:
        print_error(f"Erreur correction module: {e}")
        return False

def main():
    """Fonction principale"""
    print_header("CORRECTION DOCTR")
    
    # Installer DocTR
    doctr_installed = install_doctr()
    
    # Corriger le module
    module_fixed = fix_doctr_module()
    
    # Résultat final
    print_header("RÉSULTAT FINAL")
    
    if doctr_installed and module_fixed:
        print_success("CORRECTION DOCTR RÉUSSIE!")
        print_info("DocTR devrait maintenant fonctionner correctement")
        print_info("Relancez l'application Streamlit pour tester")
    else:
        print_error("CORRECTION DOCTR INCOMPLÈTE")
        if not doctr_installed:
            print_error("- Problèmes avec l'installation de DocTR")
        if not module_fixed:
            print_error("- Problèmes avec la correction du module")
    
    return doctr_installed and module_fixed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nCorrection interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        sys.exit(1)
