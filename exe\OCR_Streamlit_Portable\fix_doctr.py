#!/usr/bin/env python3
"""
Script de correction pour DocTR
"""
import os
import sys
import shutil
from pathlib import Path

def print_step(message):
    """Affiche une étape avec formatage"""
    print(f"\n{'='*60}")
    print(f"[STEP] {message}")
    print(f"{'='*60}")

def print_info(message):
    """Affiche une information"""
    print(f"[INFO] {message}")

def print_success(message):
    """Affiche un succès"""
    print(f"[OK] {message}")

def print_error(message):
    """Affiche une erreur"""
    print(f"[ERROR] {message}")

def print_warning(message):
    """Affiche un avertissement"""
    print(f"[WARNING] {message}")

def check_doctr_models():
    """Vérifie les modèles DocTR"""
    print_step("Vérification des modèles DocTR")
    
    # Vérifier le dossier principal
    models_dir = Path("models/doctr")
    if not models_dir.exists():
        print_error(f"Dossier DocTR manquant: {models_dir}")
        return False
    
    # Vérifier les modèles spécifiques
    det_model_path = models_dir / "db_mobilenet_v3_large" / "db_mobilenet_v3_large-21748dd0.pt"
    rec_model_path = models_dir / "crnn_vgg16_bn" / "crnn_vgg16_bn-9762b0b0.pt"
    
    models_ok = True
    
    if det_model_path.exists():
        size_mb = det_model_path.stat().st_size / (1024 * 1024)
        print_success(f"Modèle détection trouvé: {det_model_path.name} ({size_mb:.1f} MB)")
    else:
        print_error(f"Modèle détection manquant: {det_model_path}")
        models_ok = False
    
    if rec_model_path.exists():
        size_mb = rec_model_path.stat().st_size / (1024 * 1024)
        print_success(f"Modèle reconnaissance trouvé: {rec_model_path.name} ({size_mb:.1f} MB)")
    else:
        print_error(f"Modèle reconnaissance manquant: {rec_model_path}")
        models_ok = False
    
    return models_ok

def fix_doctr_wrapper():
    """Corrige le wrapper DocTR"""
    print_step("Correction du wrapper DocTR")
    
    wrapper_path = Path("backend/doctr_wrapper.py")
    if not wrapper_path.exists():
        print_error(f"Fichier wrapper manquant: {wrapper_path}")
        return False
    
    # Lire le contenu actuel
    with open(wrapper_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier s'il y a des emojis Unicode
    problematic_chars = ['🔧', '✅', '❌', '⚠️', '📁', '🌍', '🤖', '📊', '📄', '🏠', '🌐', '💥', '🧪', '📋', '📂', '🎯', '🚀', '🔄', '⏳', '🔍', '💡', '📝', '📷', '🎉']
    
    found_emojis = []
    for char in problematic_chars:
        if char in content:
            found_emojis.append(char)
    
    if found_emojis:
        print_warning(f"Emojis Unicode trouvés: {found_emojis}")
        
        # Remplacer les emojis
        replacements = {
            '🔧': '[CONFIG]',
            '✅': '[OK]',
            '❌': '[ERROR]',
            '⚠️': '[WARNING]',
            '📁': '[FOLDER]',
            '🌍': '[LANG]',
            '🤖': '[MODEL]',
            '📊': '[STATS]',
            '📄': '[FILE]',
            '🏠': '[LOCAL]',
            '🌐': '[SYSTEM]',
            '🔍': '[SEARCH]',
            '🔄': '[PROCESS]',
            '📝': '[TEXT]',
            '🧪': '[TEST]',
            '📋': '[INFO]',
            '🎉': '[SUCCESS]',
            '🧹': '[CLEAN]',
            '📷': '[IMAGE]',
            '📄': '[EXPORT]',
            '🖼️': '[IMAGE]',
            '🔁': '[IMPORT]'
        }
        
        for emoji, replacement in replacements.items():
            content = content.replace(emoji, replacement)
        
        # Sauvegarder le fichier corrigé
        with open(wrapper_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print_success(f"Wrapper DocTR corrigé: {wrapper_path}")
    else:
        print_success("Wrapper DocTR déjà corrigé")
    
    return True

def fix_doctr_module():
    """Corrige le module DocTR"""
    print_step("Correction du module DocTR")
    
    module_path = Path("backend/ocr_doctr.py")
    if not module_path.exists():
        print_error(f"Fichier module manquant: {module_path}")
        return False
    
    # Lire le contenu actuel
    with open(module_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Vérifier s'il y a des emojis Unicode
    problematic_chars = ['🔧', '✅', '❌', '⚠️', '📁', '🌍', '🤖', '📊', '📄', '🏠', '🌐', '💥', '🧪', '📋', '📂', '🎯', '🚀', '🔄', '⏳', '🔍', '💡', '📝', '📷', '🎉']
    
    found_emojis = []
    for char in problematic_chars:
        if char in content:
            found_emojis.append(char)
    
    if found_emojis:
        print_warning(f"Emojis Unicode trouvés: {found_emojis}")
        
        # Remplacer les emojis
        replacements = {
            '🔧': '[CONFIG]',
            '✅': '[OK]',
            '❌': '[ERROR]',
            '⚠️': '[WARNING]',
            '📁': '[FOLDER]',
            '🌍': '[LANG]',
            '🤖': '[MODEL]',
            '📊': '[STATS]',
            '📄': '[FILE]',
            '🏠': '[LOCAL]',
            '🌐': '[SYSTEM]',
            '🔍': '[SEARCH]',
            '🔄': '[PROCESS]',
            '📝': '[TEXT]',
            '🧪': '[TEST]',
            '📋': '[INFO]',
            '🎉': '[SUCCESS]',
            '🧹': '[CLEAN]',
            '📷': '[IMAGE]',
            '📄': '[EXPORT]',
            '🖼️': '[IMAGE]',
            '🔁': '[IMPORT]'
        }
        
        for emoji, replacement in replacements.items():
            content = content.replace(emoji, replacement)
        
        # Sauvegarder le fichier corrigé
        with open(module_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print_success(f"Module DocTR corrigé: {module_path}")
    else:
        print_success("Module DocTR déjà corrigé")
    
    return True

def main():
    """Fonction principale"""
    print_step("CORRECTION DOCTR")
    print_info("Diagnostic et correction des problèmes DocTR")
    
    # Vérifier les modèles
    models_ok = check_doctr_models()
    
    # Corriger le wrapper
    wrapper_ok = fix_doctr_wrapper()
    
    # Corriger le module
    module_ok = fix_doctr_module()
    
    # Résultat final
    print_step("RÉSULTAT FINAL")
    
    if models_ok and wrapper_ok and module_ok:
        print_success("CORRECTION DOCTR RÉUSSIE!")
        print_info("DocTR devrait maintenant fonctionner correctement")
        print_info("Relancez l'application Streamlit pour tester")
    else:
        print_error("CORRECTION DOCTR INCOMPLÈTE")
        if not models_ok:
            print_error("- Problèmes avec les modèles DocTR")
        if not wrapper_ok:
            print_error("- Problèmes avec le wrapper DocTR")
        if not module_ok:
            print_error("- Problèmes avec le module DocTR")
    
    return models_ok and wrapper_ok and module_ok

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nCorrection interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        sys.exit(1)
