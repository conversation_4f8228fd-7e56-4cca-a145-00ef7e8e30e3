# Guide de création de l'installateur OCR Intelligent

## Vue d'ensemble

Ce guide vous explique comment créer un installateur Windows professionnel pour l'application OCR Intelligent. L'installateur final permettra aux utilisateurs d'installer l'application avec tous ses composants (Tesseract, EasyOCR, DocTR) sans nécessiter d'installation Python préalable.

## Prérequis

### 1. Logiciels requis

#### Python 3.8+
- Téléchargez depuis [python.org](https://python.org)
- Assurez-vous que Python est dans le PATH système
- Vérifiez avec `python --version`

#### Inno Setup (pour l'installateur Windows)
- Téléchargez depuis [jrsoftware.org](https://jrsoftware.org/isinfo.php)
- Installez avec les options par défaut
- Version recommandée : Inno Setup 6

#### Dépendances Python (installées automatiquement)
- PyInstaller : Pour empaqueter l'application
- Pillow : Pour créer l'icône de l'application

### 2. Vérification de l'environnement

Avant de commencer, assurez-vous que :
- L'application OCR fonctionne correctement
- Tous les modèles sont présents dans le dossier `models/`
- Python peut importer tous les modules nécessaires

## Méthodes de création

### Méthode 1 : Script automatique (Recommandée)

1. **Ouvrez une invite de commande** dans le dossier `installer/`
2. **Exécutez le script** : Double-cliquez sur `create_installer.bat`
3. **Suivez les instructions** affichées à l'écran
4. **Attendez la fin** de la construction (peut prendre 10-30 minutes)

### Méthode 2 : Étapes manuelles

#### Étape 1 : Préparation
```bash
cd installer/
python create_icon.py
```

#### Étape 2 : Construction de l'exécutable
```bash
python build_installer.py
```

#### Étape 3 : Création de l'installateur
```bash
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" ocr_setup.iss
```

## Structure des fichiers générés

Après la construction, vous obtiendrez :

```
installer/
├── dist/
│   ├── OCR_Intelligent/          # Application portable
│   │   ├── OCR_Intelligent.exe   # Exécutable principal
│   │   ├── _internal/            # Dépendances Python
│   │   ├── models/               # Modèles OCR
│   │   └── ...
│   └── OCR_Intelligent_Setup.exe # Installateur Windows
├── build/                        # Fichiers temporaires
└── app/                          # Copie de l'application
```

## Personnalisation

### Modifier les informations de l'application

Éditez le fichier `ocr_setup.iss` :
```pascal
#define MyAppName "Votre Nom d'Application"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Votre Organisation"
#define MyAppURL "https://votre-site.com"
```

### Changer l'icône

1. Remplacez le fichier `ocr_icon.ico` par votre icône
2. Ou modifiez le script `create_icon.py` pour générer une icône personnalisée

### Ajouter de la documentation

Placez vos fichiers de documentation dans le dossier `documentation/` :
- Guide utilisateur
- Notes de version
- Licence
- FAQ

## Test et validation

### 1. Test de l'application portable

1. Naviguez vers `dist/OCR_Intelligent/`
2. Exécutez `OCR_Intelligent.exe`
3. Vérifiez que l'application se lance correctement
4. Testez les trois moteurs OCR

### 2. Test de l'installateur

1. Copiez `OCR_Intelligent_Setup.exe` sur une machine propre
2. Exécutez l'installateur
3. Vérifiez l'installation dans le menu Démarrer
4. Testez l'application installée

### 3. Test d'installation silencieuse

```bash
OCR_Intelligent_Setup.exe /VERYSILENT /SUPPRESSMSGBOXES
```

## Déploiement en entreprise

### Installation silencieuse avec paramètres

```bash
# Installation silencieuse dans un répertoire spécifique
OCR_Intelligent_Setup.exe /VERYSILENT /DIR="C:\Applications\OCR_Intelligent"

# Installation sans redémarrage
OCR_Intelligent_Setup.exe /VERYSILENT /NORESTART

# Installation complète silencieuse
OCR_Intelligent_Setup.exe /VERYSILENT /SUPPRESSMSGBOXES /DIR="C:\Applications\OCR_Intelligent" /NORESTART
```

### Déploiement par GPO

1. Placez l'installateur sur un partage réseau
2. Créez une GPO pour déployer l'application
3. Utilisez les paramètres d'installation silencieuse

## Résolution des problèmes

### Erreurs courantes

#### "PyInstaller failed"
- Vérifiez que toutes les dépendances sont installées
- Assurez-vous que l'application fonctionne avant l'empaquetage
- Consultez les logs dans le dossier `build/`

#### "Inno Setup not found"
- Installez Inno Setup depuis le site officiel
- Vérifiez le chemin d'installation
- Modifiez le script si nécessaire

#### "Application crashes on startup"
- Vérifiez que tous les modèles sont inclus
- Testez l'application portable avant de créer l'installateur
- Consultez les logs d'erreur

### Optimisation de la taille

Pour réduire la taille de l'installateur :

1. **Supprimez les modèles inutiles** du dossier `models/`
2. **Excluez les modules non nécessaires** dans le fichier spec
3. **Activez la compression UPX** (déjà activée par défaut)
4. **Utilisez la compression LZMA2** dans Inno Setup

### Logs et débogage

- **Logs PyInstaller** : `build/OCR_Intelligent/warn-OCR_Intelligent.txt`
- **Logs Inno Setup** : Affichés dans la console
- **Logs application** : Consultez la sortie console de l'application

## Support et maintenance

### Mise à jour de l'application

1. Modifiez le code source
2. Incrémentez le numéro de version dans `ocr_setup.iss`
3. Reconstruisez l'installateur
4. Testez la mise à jour

### Distribution

- **Site web** : Hébergez l'installateur sur votre site
- **Partage réseau** : Pour déploiement en entreprise
- **USB** : Pour installation hors ligne
- **Email** : Pour distribution limitée (attention à la taille)

## Checklist finale

Avant de distribuer l'installateur :

- [ ] L'application fonctionne correctement
- [ ] Tous les moteurs OCR sont opérationnels
- [ ] L'installateur se lance sans erreur
- [ ] L'installation silencieuse fonctionne
- [ ] La désinstallation est propre
- [ ] La documentation est à jour
- [ ] Les informations de version sont correctes
- [ ] L'icône s'affiche correctement
- [ ] Les raccourcis sont créés
- [ ] L'application se lance après installation

## Contact

Pour toute question ou problème :
- Email : <EMAIL>
- Documentation : https://votre-site.com/docs
- Support technique : https://votre-site.com/support
