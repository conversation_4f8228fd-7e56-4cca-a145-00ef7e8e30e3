# Output Directory

This directory contains the results generated by the OCR Intelligent application.

## Generated Files

### Standard OCR Mode
- `result_[method].docx` - Word documents with OCR results
- `result_[method].txt` - Plain text results

### Layout Detection Mode
- `structured_result_[method].docx` - Word documents with preserved structure
- `structured_result_[image].json` - Detailed JSON with zone information
- `detailed_structured_results.json` - Complete analysis results

### Debug Files
- `debug/[image]_layout_debug.jpg` - Annotated images showing detected zones

## Directory Structure

```
output/
├── README.md                    # This file
├── backend/                     # Temporary backend files (auto-cleaned)
├── debug/                       # Debug images (auto-cleaned)
├── *.docx                       # Word documents (auto-cleaned)
├── *.json                       # JSON results (auto-cleaned)
└── *.txt                        # Text files (auto-cleaned)
```

## Automatic Cleanup

The application automatically cleans this directory on startup to maintain a clean state. Generated files are temporary and will be removed on the next application launch.

## Preservation

To preserve results:
1. Download files immediately after processing
2. Save to a different location outside this directory
3. Use the application's download buttons in the interface
