# Création de l'installateur OCR Intelligent

Ce dossier contient les scripts nécessaires pour créer un installateur Windows complet pour l'application OCR Intelligent.

## Prérequis

### Logiciels requis
- **Python 3.8+** : Pour exécuter les scripts de construction
- **PyInstaller** : Pour empaqueter l'application Python (installé automatiquement)
- **Inno Setup** : Pour créer l'installateur Windows (à installer manuellement)

### Installation d'Inno Setup
1. Téléchargez Inno Setup depuis [le site officiel](https://jrsoftware.org/isinfo.php)
2. Installez-le avec les options par défaut
3. Assurez-vous que le chemin d'installation est l'un des suivants :
   - `C:\Program Files (x86)\Inno Setup 6\`
   - `C:\Program Files\Inno Setup 6\`
   - `C:\Program Files (x86)\Inno Setup 5\`
   - `C:\Program Files\Inno Setup 5\`

## Fichiers inclus

- **build.bat** : Script principal pour lancer la construction
- **build_installer.py** : Script Python pour empaqueter l'application
- **create_icon.py** : Script pour créer l'icône de l'application
- **ocr_setup.iss** : Script Inno Setup pour créer l'installateur Windows
- **documentation/** : Documentation utilisateur incluse dans l'installateur

## Processus de construction

### Méthode simple (recommandée)
1. Double-cliquez sur `build.bat`
2. Suivez les instructions à l'écran
3. Les fichiers générés seront dans le dossier `dist/`

### Méthode manuelle
1. Créez l'icône de l'application : `python create_icon.py`
2. Construisez l'exécutable : `python build_installer.py`
3. Créez l'installateur avec Inno Setup : `"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" ocr_setup.iss`

## Résultats

La construction génère deux éléments principaux :
1. **Application portable** : Dossier `dist/OCR_Intelligent/` contenant l'application exécutable
2. **Installateur Windows** : Fichier `dist/OCR_Intelligent_Setup.exe` pour installation complète

## Personnalisation

### Modification de l'application
- Modifiez les fichiers dans le dossier parent avant de lancer la construction

### Modification de l'installateur
- **Nom et version** : Modifiez les variables en haut du fichier `ocr_setup.iss`
- **Icône** : Remplacez le fichier généré `ocr_icon.ico` par votre propre icône
- **Documentation** : Mettez à jour les fichiers dans le dossier `documentation/`

### Options avancées
- **Installation silencieuse** : Utilisez `/SILENT` ou `/VERYSILENT` lors de l'installation
- **Personnalisation** : Modifiez le fichier `ocr_setup.iss` pour ajouter des fonctionnalités

## Déploiement en entreprise

Pour un déploiement en entreprise, vous pouvez :
1. Utiliser l'installation silencieuse : `OCR_Intelligent_Setup.exe /VERYSILENT /SUPPRESSMSGBOXES`
2. Spécifier le répertoire d'installation : `/DIR="C:\Applications\OCR_Intelligent"`
3. Désactiver le lancement automatique : `/NORESTART`

Exemple complet :
```
OCR_Intelligent_Setup.exe /VERYSILENT /SUPPRESSMSGBOXES /DIR="C:\Applications\OCR_Intelligent" /NORESTART
```

## Résolution des problèmes

### Erreurs courantes

#### "PyInstaller non trouvé"
- Le script `build.bat` devrait l'installer automatiquement
- Sinon, installez-le manuellement : `pip install pyinstaller`

#### "Inno Setup non trouvé"
- Installez Inno Setup depuis [le site officiel](https://jrsoftware.org/isinfo.php)
- Vérifiez que le chemin d'installation est correct

#### "Erreur lors de la construction"
- Vérifiez que l'application fonctionne correctement avant la construction
- Assurez-vous que toutes les dépendances sont installées
- Consultez les logs d'erreur pour plus de détails

## Support

Pour toute question ou problème, contactez :
- Email : <EMAIL>
- Site web : https://votre-site.com
