# Guide d'utilisation - OCR Intelligent

## Bienvenue dans OCR Intelligent

OCR Intelligent est une application de reconnaissance optique de caractères (OCR) qui utilise trois moteurs OCR différents pour extraire le texte de vos images et documents.

## Fonctionnalités principales

### Moteurs OCR disponibles
- **Tesseract** : Moteur OCR open-source de Google, excellent pour les documents de qualité
- **EasyOCR** : Moteur basé sur l'intelligence artificielle, performant sur les images complexes
- **DocTR** : Moteur spécialisé dans les documents, optimisé pour la reconnaissance de texte structuré

### Formats supportés
- **Images** : PNG, JPG, JPEG, BMP, TIFF
- **Documents** : PDF (conversion automatique en images)

### Langues supportées
- Français
- Anglais
- (Autres langues selon le moteur utilisé)

## Démarrage rapide

### 1. Lancement de l'application
- Double-cliquez sur l'icône "OCR Intelligent" sur votre bureau
- Ou utilisez le menu Démarrer > OCR Intelligent
- L'application s'ouvrira dans votre navigateur web par défaut

### 2. Interface utilisateur
L'interface se compose de plusieurs sections :
- **Zone de téléchargement** : Glissez-déposez vos fichiers ou utilisez le bouton "Parcourir"
- **Sélection du moteur OCR** : Choisissez entre Tesseract, EasyOCR ou DocTR
- **Options de traitement** : Configurez les paramètres selon vos besoins
- **Résultats** : Visualisez le texte extrait et les scores de confiance

### 3. Traitement d'un document
1. **Téléchargez votre fichier** dans la zone prévue à cet effet
2. **Sélectionnez le moteur OCR** approprié :
   - Tesseract pour les documents scannés de bonne qualité
   - EasyOCR pour les images avec du texte complexe
   - DocTR pour les documents structurés
3. **Cliquez sur "Analyser"** pour lancer le traitement
4. **Consultez les résultats** dans les onglets correspondants

## Guide détaillé

### Choix du moteur OCR

#### Tesseract
- **Avantages** : Rapide, précis sur les documents de qualité, nombreuses langues
- **Inconvénients** : Sensible à la qualité de l'image
- **Recommandé pour** : Documents scannés, texte imprimé de bonne qualité

#### EasyOCR
- **Avantages** : Robuste aux variations d'éclairage, fonctionne bien sur les photos
- **Inconvénients** : Plus lent que Tesseract
- **Recommandé pour** : Photos de documents, images avec arrière-plan complexe

#### DocTR
- **Avantages** : Optimisé pour les documents, bonne reconnaissance de la structure
- **Inconvénients** : Peut être plus lent sur de gros documents
- **Recommandé pour** : Factures, formulaires, documents structurés

### Optimisation des résultats

#### Qualité de l'image
- Utilisez des images de haute résolution (300 DPI minimum)
- Assurez-vous que le texte est net et lisible
- Évitez les ombres et les reflets

#### Préparation du document
- Redressez les documents inclinés
- Améliorez le contraste si nécessaire
- Supprimez les éléments parasites (taches, pliures)

#### Paramètres avancés
- Ajustez le seuil de confiance selon vos besoins
- Sélectionnez la langue appropriée
- Utilisez le prétraitement d'image si disponible

### Export des résultats

L'application permet d'exporter les résultats dans plusieurs formats :
- **Texte brut** (.txt)
- **Document Word** (.docx)
- **Fichier Excel** (.xlsx)
- **PDF avec texte** (.pdf)

## Résolution des problèmes

### Problèmes courants

#### L'application ne se lance pas
- Vérifiez que vous avez au moins 4 Go de RAM disponible
- Assurez-vous d'avoir au moins 2 Go d'espace disque libre
- Redémarrez votre ordinateur et réessayez

#### Résultats OCR de mauvaise qualité
- Vérifiez la qualité de votre image source
- Essayez un autre moteur OCR
- Ajustez les paramètres de prétraitement

#### L'application est lente
- Fermez les autres applications pour libérer de la mémoire
- Utilisez des images de taille raisonnable (< 10 Mo)
- Évitez de traiter plusieurs documents simultanément

### Messages d'erreur

#### "Erreur de chargement du modèle"
- Redémarrez l'application
- Vérifiez l'intégrité de l'installation
- Contactez le support technique si le problème persiste

#### "Format de fichier non supporté"
- Vérifiez que votre fichier est dans un format supporté
- Convertissez votre fichier si nécessaire
- Utilisez un fichier de test pour vérifier le fonctionnement

## Configuration avancée

### Paramètres de l'application
Les paramètres sont accessibles via le fichier `config.json` dans le répertoire d'installation :
- Seuils de confiance par défaut
- Langues préférées
- Répertoires de sortie
- Options de prétraitement

### Modèles personnalisés
L'application utilise des modèles pré-entraînés inclus dans l'installation. Pour des besoins spécifiques, contactez le support technique.

## Support technique

### Informations système
Pour obtenir de l'aide, préparez les informations suivantes :
- Version de Windows
- Version d'OCR Intelligent
- Description détaillée du problème
- Fichiers de test (si applicable)

### Contact
- Email : <EMAIL>
- Site web : https://votre-site.com
- Documentation en ligne : https://votre-site.com/docs

## Licence et mentions légales

OCR Intelligent utilise les technologies suivantes :
- Tesseract OCR (Apache License 2.0)
- EasyOCR (Apache License 2.0)
- DocTR (Apache License 2.0)
- Streamlit (Apache License 2.0)

© 2024 Votre Organisation. Tous droits réservés.

---

**Version du guide** : 1.0.0  
**Dernière mise à jour** : Décembre 2024
