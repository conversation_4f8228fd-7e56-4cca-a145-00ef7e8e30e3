#!/usr/bin/env python3
"""
Script d'installation de DocTR à partir du wheel local - Version simplifiée
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """Fonction principale"""
    print("="*60)
    print("INSTALLATION DOCTR")
    print("="*60)
    
    # Vérifier si le wheel existe
    wheel_path = Path("models/python_doctr-1.0.0-py3-none-any.whl")
    if not wheel_path.exists():
        print(f"[ERROR] Wheel DocTR manquant: {wheel_path}")
        return False
    
    print(f"[STEP] Installation de DocTR depuis {wheel_path}")
    
    try:
        # Installer le wheel
        subprocess.run(
            [sys.executable, "-m", "pip", "install", str(wheel_path)],
            check=True
        )
        
        print("[OK] DocTR installé avec succès")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Erreur installation DocTR: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] Erreur inattendue: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nInstallation interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        sys.exit(1)
