#!/usr/bin/env python3
"""
Script pour créer une icône pour l'application OCR
"""
import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def create_ocr_icon(output_path, size=256):
    """Crée une icône simple pour l'application OCR"""
    try:
        # Créer une image carrée avec fond bleu
        img = Image.new('RGBA', (size, size), (0, 102, 204, 255))
        draw = ImageDraw.Draw(img)
        
        # Dessiner un cercle blanc au centre
        center = size // 2
        radius = size // 3
        draw.ellipse((center - radius, center - radius, center + radius, center + radius), fill=(255, 255, 255, 255))
        
        # Ajouter le texte "OCR"
        try:
            # Essayer de charger une police
            font_size = size // 4
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # Fallback vers la police par défaut
            font = ImageFont.load_default()
        
        text = "OCR"
        text_width, text_height = draw.textsize(text, font=font) if hasattr(draw, 'textsize') else (font_size * 3, font_size)
        
        # Positionner le texte au centre
        text_position = (center - text_width // 2, center - text_height // 2)
        
        # Dessiner le texte en bleu
        draw.text(text_position, text, font=font, fill=(0, 51, 153, 255))
        
        # Sauvegarder l'image en format ICO
        img.save(output_path, format="ICO")
        
        print(f"Icône créée avec succès: {output_path}")
        return True
        
    except Exception as e:
        print(f"Erreur lors de la création de l'icône: {e}")
        return False

def main():
    """Fonction principale"""
    # Chemin de sortie pour l'icône
    output_path = Path(__file__).parent / "ocr_icon.ico"
    
    # Créer l'icône
    success = create_ocr_icon(output_path)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
