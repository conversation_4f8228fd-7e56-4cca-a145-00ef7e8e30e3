@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================================================
echo INSTALLATION D'INNO SETUP - OCR INTELLIGENT
echo ================================================================
echo.

:: Vérifier si Inno Setup est déjà installé
echo [STEP] Vérification de l'installation d'Inno Setup...

set INNO_FOUND=0
set INNO_PATH=

:: Vérifier les emplacements standards
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set INNO_FOUND=1
    set INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe
)
if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set INNO_FOUND=1
    set INNO_PATH=C:\Program Files\Inno Setup 6\ISCC.exe
)
if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    set INNO_FOUND=1
    set INNO_PATH=C:\Program Files (x86)\Inno Setup 5\ISCC.exe
)
if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    set INNO_FOUND=1
    set INNO_PATH=C:\Program Files\Inno Setup 5\ISCC.exe
)

if !INNO_FOUND! equ 1 (
    echo [OK] Inno Setup trouvé: !INNO_PATH!
    echo.
    echo Inno Setup est déjà installé. Aucune action nécessaire.
    echo Vous pouvez maintenant créer l'installateur OCR.
    echo.
    pause
    exit /b 0
)

echo [INFO] Inno Setup non trouvé
echo.

:: Proposer l'installation automatique
echo Ce script peut télécharger et installer Inno Setup automatiquement.
echo.
echo Options disponibles :
echo 1. Installation automatique (recommandée)
echo 2. Installation manuelle
echo 3. Annuler
echo.
set /p CHOICE="Votre choix (1-3) : "

if "%CHOICE%"=="1" goto :auto_install
if "%CHOICE%"=="2" goto :manual_install
if "%CHOICE%"=="3" goto :cancel
goto :invalid_choice

:auto_install
echo.
echo [STEP] Installation automatique d'Inno Setup...
python install_inno_setup.py
set INSTALL_RESULT=!errorlevel!

if !INSTALL_RESULT! equ 0 (
    echo.
    echo [OK] Installation automatique réussie
    goto :verify_final
) else (
    echo.
    echo [ERROR] L'installation automatique a échoué
    echo Basculement vers l'installation manuelle...
    goto :manual_install
)

:manual_install
echo.
echo [STEP] Installation manuelle d'Inno Setup
echo.
echo Instructions pour l'installation manuelle :
echo.
echo 1. Ouvrez votre navigateur web
echo 2. Allez sur https://jrsoftware.org/isinfo.php
echo 3. Cliquez sur "Download Inno Setup"
echo 4. Téléchargez "Inno Setup 6.x.x"
echo 5. Exécutez le fichier téléchargé
echo 6. Suivez l'assistant d'installation avec les options par défaut
echo 7. Redémarrez cette invite de commande après l'installation
echo.

:: Ouvrir automatiquement le site web
echo [INFO] Ouverture du site web d'Inno Setup...
start https://jrsoftware.org/isinfo.php

echo.
echo Appuyez sur une touche après avoir installé Inno Setup...
pause >nul

goto :verify_final

:verify_final
echo.
echo [STEP] Vérification finale de l'installation...

:: Revérifier l'installation
set INNO_FOUND=0
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" set INNO_FOUND=1
if exist "C:\Program Files\Inno Setup 6\ISCC.exe" set INNO_FOUND=1
if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" set INNO_FOUND=1
if exist "C:\Program Files\Inno Setup 5\ISCC.exe" set INNO_FOUND=1

if !INNO_FOUND! equ 1 (
    echo [OK] Inno Setup correctement installé
    echo.
    echo ================================================================
    echo INSTALLATION RÉUSSIE
    echo ================================================================
    echo.
    echo ✓ Inno Setup est maintenant installé
    echo ✓ Vous pouvez créer l'installateur OCR
    echo.
    echo Prochaines étapes :
    echo 1. Exécutez check_ready.py pour vérifier toutes les dépendances
    echo 2. Lancez create_installer.bat pour créer l'installateur
    echo.
    set EXIT_CODE=0
) else (
    echo [ERROR] Inno Setup n'a pas été installé correctement
    echo.
    echo ================================================================
    echo INSTALLATION ÉCHOUÉE
    echo ================================================================
    echo.
    echo Solutions possibles :
    echo 1. Réessayez l'installation manuelle
    echo 2. Vérifiez que vous avez les droits administrateur
    echo 3. Redémarrez votre ordinateur et réessayez
    echo 4. Contactez le support technique
    echo.
    set EXIT_CODE=1
)

echo Appuyez sur une touche pour fermer...
pause >nul
exit /b !EXIT_CODE!

:invalid_choice
echo.
echo [ERROR] Choix invalide. Veuillez entrer 1, 2 ou 3.
echo.
pause
exit /b 1

:cancel
echo.
echo [INFO] Installation annulée par l'utilisateur
echo.
echo Pour créer l'installateur OCR, vous devrez installer Inno Setup manuellement :
echo https://jrsoftware.org/isinfo.php
echo.
pause
exit /b 1
