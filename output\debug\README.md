# Debug Directory

This directory contains debug images generated by the layout detection feature.

## Debug Images

When layout detection is enabled, the application generates annotated images showing:

- **Detected zones** with colored bounding boxes
- **Zone types** with labels (text, title, table, figure)
- **Confidence scores** for each detected zone

## File Naming

Debug images follow this naming pattern:
- `[original_image_name]_layout_debug.jpg`

## Zone Color Coding

- **Green** - Text zones
- **Red** - Title zones  
- **Blue** - List zones
- **Cyan** - Table zones
- **Magenta** - Figure zones

## Automatic Cleanup

Debug images are automatically cleaned on application startup to maintain disk space.

## Usage

Debug images are generated when:
1. Layout detection mode is enabled
2. `enable_debug=True` is set in the processing options
3. The application successfully detects zones in the image
