Looking in indexes: https://artifacts.cloud.safran/repository/pypi-group/simple
Collecting layoutparser[paddledetection]
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/layoutparser/0.3.4/layoutparser-0.3.4-py3-none-any.whl (19.2 MB)
     ---------------------------------------- 19.2/19.2 MB 966.1 kB/s eta 0:00:00
Requirement already satisfied: numpy in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (2.3.1)
Requirement already satisfied: opencv-python in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (*********)
Requirement already satisfied: scipy in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (1.15.3)
Requirement already satisfied: pandas in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (2.3.1)
Requirement already satisfied: pillow in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (10.3.0)
Requirement already satisfied: pyyaml>=5.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (6.0.2)
Collecting iopath (from layoutparser[paddledetection])
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/iopath/0.1.10/iopath-0.1.10.tar.gz (42 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting pdfplumber (from layoutparser[paddledetection])
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/pdfplumber/0.11.7/pdfplumber-0.11.7-py3-none-any.whl (60 kB)
Collecting pdf2image (from layoutparser[paddledetection])
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/pdf2image/1.17.0/pdf2image-1.17.0-py3-none-any.whl (11 kB)
INFO: pip is looking at multiple versions of layoutparser[paddledetection] to determine which version is compatible with other requirements. This could take a while.
Collecting layoutparser[paddledetection]
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/layoutparser/0.3.3/layoutparser-0.3.3-py3-none-any.whl (19.2 MB)
     ---------------------------------------- 19.2/19.2 MB 904.1 kB/s eta 0:00:00
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/layoutparser/0.3.2/layoutparser-0.3.2-py3-none-any.whl (19.2 MB)
     ---------------------------------------- 19.2/19.2 MB 1.1 MB/s eta 0:00:00
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/layoutparser/0.3.1/layoutparser-0.3.1-py3-none-any.whl (19.2 MB)
     ---------------------------------------- 19.2/19.2 MB 718.0 kB/s eta 0:00:00
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/layoutparser/0.3.0/layoutparser-0.3.0-py3-none-any.whl (19.2 MB)
     ---------------------------------------- 19.2/19.2 MB 636.7 kB/s eta 0:00:00
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/layoutparser/0.2.0/layoutparser-0.2.0-py3-none-any.whl (19.1 MB)
     ---------------------------------------- 19.1/19.1 MB 889.0 kB/s eta 0:00:00
Requirement already satisfied: torch in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (2.7.0)
Requirement already satisfied: torchvision in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from layoutparser[paddledetection]) (0.22.0)
Requirement already satisfied: tqdm in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from iopath->layoutparser[paddledetection]) (4.67.1)
Requirement already satisfied: typing_extensions in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from iopath->layoutparser[paddledetection]) (4.13.2)
Collecting portalocker (from iopath->layoutparser[paddledetection])
  Downloading https://artifacts.cloud.safran/repository/pypi-group/packages/portalocker/3.2.0/portalocker-3.2.0-py3-none-any.whl (22 kB)
Requirement already satisfied: python-dateutil>=2.8.2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pandas->layoutparser[paddledetection]) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pandas->layoutparser[paddledetection]) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from pandas->layoutparser[paddledetection]) (2025.2)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from python-dateutil>=2.8.2->pandas->layoutparser[paddledetection]) (1.17.0)
Requirement already satisfied: pywin32>=226 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from portalocker->iopath->layoutparser[paddledetection]) (310)
Requirement already satisfied: filelock in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (3.18.0)
Requirement already satisfied: sympy>=1.13.3 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (1.14.0)
Requirement already satisfied: networkx in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (3.1)
Requirement already satisfied: jinja2 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (3.1.6)
Requirement already satisfied: fsspec in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from torch->layoutparser[paddledetection]) (2025.3.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from sympy>=1.13.3->torch->layoutparser[paddledetection]) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from jinja2->torch->layoutparser[paddledetection]) (3.0.2)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\local\programs\python\python311\lib\site-packages (from tqdm->iopath->layoutparser[paddledetection]) (0.4.6)
Building wheels for collected packages: iopath
  Building wheel for iopath (setup.py): started
  Building wheel for iopath (setup.py): finished with status 'done'
  Created wheel for iopath: filename=iopath-0.1.10-py3-none-any.whl size=31538 sha256=c1d318e0d7fcf6c11e1eea45d01cce4313505c4330ed1ed5c575a80f4e2e8e29
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\77\e3\06\8c03b75722693cb1c92587b9259827647fcbd1065632646255
Successfully built iopath
Installing collected packages: portalocker, iopath, layoutparser

Successfully installed iopath-0.1.10 layoutparser-0.2.0 portalocker-3.2.0
