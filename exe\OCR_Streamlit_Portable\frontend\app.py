import os
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"] = ""
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"
import warnings
warnings.filterwarnings("ignore")
import streamlit as st
import os, sys
import fitz
from PIL import Image
from streamlit.components.v1 import html
import docx
import glob
import shutil
from pathlib import Path

# [IMPORT] Ajout du chemin backend
# Déterminer le chemin de base de l'application
if getattr(sys, 'frozen', False):
    # Si l'application est empaquetée avec PyInstaller
    base_path = Path(sys._MEIPASS)
    backend_path = base_path / "backend"
else:
    # En développement
    base_path = Path(__file__).parent.parent
    backend_path = base_path / "backend"

sys.path.insert(0, str(base_path))
from backend.main import run_all_ocr_methods
from backend.export import export_to_word


def clear_output_directory():
    """
    Nettoie le dossier output à chaque relancement de l'application
    Supprime tous les fichiers .docx générés précédemment
    """
    # Utiliser un chemin relatif à l'application
    if getattr(sys, 'frozen', False):
        # Si l'application est empaquetée avec PyInstaller
        output_dir = os.path.join(os.path.dirname(sys.executable), "output")
    else:
        # En développement
        output_dir = os.path.join(os.path.dirname(__file__), "..", "output")

    if os.path.exists(output_dir):
        try:
            # Compter les fichiers avant nettoyage
            files_before = glob.glob(os.path.join(output_dir, "*.docx"))
            files_count = len(files_before)

            if files_count > 0:
                # Supprimer tous les fichiers .docx
                for file_path in files_before:
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        print(f"[WARNING] Impossible de supprimer {file_path}: {e}")

                # Vérifier le nettoyage
                files_after = glob.glob(os.path.join(output_dir, "*.docx"))
                cleaned_count = files_count - len(files_after)

                if cleaned_count > 0:
                    print(f"[CLEAN] Dossier output nettoyé: {cleaned_count} fichier(s) supprimé(s)")
            else:
                print("[OK] Dossier output déjà propre")

        except Exception as e:
            print(f"[ERROR] Erreur lors du nettoyage du dossier output: {e}")
    else:
        # Créer le dossier s'il n'existe pas
        os.makedirs(output_dir, exist_ok=True)
        print(f"[FOLDER] Dossier output créé: {output_dir}")

    return output_dir


# [CLEAN] Nettoyage automatique du dossier output au démarrage
if 'output_cleaned' not in st.session_state:
    clear_output_directory()
    st.session_state.output_cleaned = True

st.set_page_config(page_title="OCR Intelligent", layout="wide")
st.image("frontend/safran_logo.png", width=250)
with open("frontend/custom_style.html") as f:
    html(f.read(), height=0)
st.markdown("<h1> OCR Intelligent </h1>", unsafe_allow_html=True)

uploaded_file = st.file_uploader(" Téléversez une image ou un PDF", type=["png", "jpg", "jpeg", "pdf"])

if uploaded_file:
    os.makedirs("images", exist_ok=True)
    os.makedirs("output", exist_ok=True)
    file_path = os.path.join("images", uploaded_file.name)

    if uploaded_file.type == "application/pdf":
        doc = fitz.open(stream=uploaded_file.read(), filetype="pdf")
        st.warning(f"PDF détecté, traitement de {len(doc)} pages.")
        image_paths = []
        for i, page in enumerate(doc):
            pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
            image_path = os.path.join("images", f"page_{i+1}.png")
            pix.save(image_path)
            image_paths.append(image_path)
    else:
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        image_paths = [file_path]

    # --- OCR multiple
    with st.spinner(" Analyse en cours, merci de patienter..."):
        results, best_method, word_file = run_all_ocr_methods(image_paths)
    st.success(" Analyse terminée !")

    # --- Affichage
    col_img, col_text = st.columns([1, 3])
    with col_img:
        st.image(image_paths[0], caption=" Image analysée")

    with col_text:
        cols = st.columns(len(results))
        for i, (method, data) in enumerate(results.items()):
            with cols[i]:
                st.markdown(f"###  {method.upper()} ({data['avg_conf']:.2f}%)")
                st.text_area("Texte extrait", "\n".join(data['lines']), height=250, key=f"text_{method}")

    st.success(f" Meilleure méthode : {best_method.upper()}")
    with open(word_file, "rb") as f:
        st.download_button(" Télécharger le Word", f, file_name=os.path.basename(word_file))

# --- Upload d'un Word corrigé ---
st.markdown("---")
st.markdown("### Uploader un Word corrigé")
corrected_file = st.file_uploader("Déposez ici votre Word corrigé", type=["docx"], key="corrected")
if corrected_file is not None:
    os.makedirs("corrected", exist_ok=True)
    # Extraire le texte du Word corrigé
    doc = docx.Document(corrected_file)
    corrected_text = "\n".join([p.text for p in doc.paragraphs])
    
    # Sauvegarder la correction
    base_name = uploaded_file.name if uploaded_file else "unknown"
    txt_name = os.path.splitext(base_name)[0] + "_corrige.txt"
    corrected_path = os.path.join("corrected", txt_name)
    os.makedirs("corrected", exist_ok=True)

    with open(corrected_path, "w", encoding="utf-8") as f:
        f.write(corrected_text)
    st.success(f"Texte corrigé enregistré dans {corrected_path}")


def main():
    """Fonction principale de l'application"""
    # Nettoyer le dossier output au démarrage
    clear_output_directory()

    # Configuration de la page Streamlit
    st.set_page_config(
        page_title="OCR Intelligent",
        page_icon="📄",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Charger le CSS personnalisé
    try:
        with open(os.path.join(os.path.dirname(__file__), "custom_style.html"), "r", encoding="utf-8") as f:
            html(f.read(), height=0)
    except FileNotFoundError:
        pass  # Ignorer si le fichier CSS n'existe pas

    # Interface principale
    st.title("📄 OCR Intelligent")
    st.markdown("### Reconnaissance optique de caractères avec Tesseract, EasyOCR et DocTR")

    # Sidebar pour les options
    with st.sidebar:
        st.header("⚙️ Configuration")

        # Sélection du moteur OCR
        ocr_engine = st.selectbox(
            "Moteur OCR",
            ["Tous", "Tesseract", "EasyOCR", "DocTR"],
            help="Choisissez le moteur OCR à utiliser"
        )

        # Options avancées
        with st.expander("Options avancées"):
            confidence_threshold = st.slider(
                "Seuil de confiance (%)",
                min_value=0,
                max_value=100,
                value=30,
                help="Seuil minimum de confiance pour afficher le texte"
            )

            preprocess = st.checkbox(
                "Prétraitement d'image",
                value=True,
                help="Améliore la qualité de l'image avant OCR"
            )

    # Zone de téléchargement
    uploaded_file = st.file_uploader(
        "Téléversez une image ou un PDF",
        type=["png", "jpg", "jpeg", "bmp", "tiff", "pdf"],
        help="Formats supportés: PNG, JPG, JPEG, BMP, TIFF, PDF"
    )

    if uploaded_file is not None:
        # Afficher l'image téléchargée
        if uploaded_file.type.startswith('image'):
            image = Image.open(uploaded_file)
            st.image(image, caption="Image téléchargée", use_column_width=True)

        # Bouton d'analyse
        if st.button("🔍 Analyser", type="primary"):
            with st.spinner("Analyse en cours..."):
                try:
                    # Exécuter l'OCR
                    results = run_all_ocr_methods(uploaded_file)

                    # Afficher les résultats
                    if results:
                        st.success("Analyse terminée!")

                        # Créer des onglets pour chaque moteur
                        tabs = st.tabs(["Tesseract", "EasyOCR", "DocTR"])

                        for i, (engine, (lines, confidences)) in enumerate(results.items()):
                            with tabs[i]:
                                st.subheader(f"Résultats {engine}")

                                if lines:
                                    # Afficher le texte extrait
                                    text_content = "\n".join(lines)
                                    st.text_area(
                                        "Texte extrait",
                                        value=text_content,
                                        height=300,
                                        key=f"text_{engine}"
                                    )

                                    # Afficher les statistiques
                                    avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                                    st.metric("Confiance moyenne", f"{avg_confidence:.1f}%")
                                    st.metric("Lignes détectées", len(lines))

                                    # Bouton d'export
                                    if st.button(f"📄 Exporter en Word", key=f"export_{engine}"):
                                        try:
                                            output_path = export_to_word(lines, uploaded_file.name, engine)
                                            st.success(f"Document exporté: {output_path}")
                                        except Exception as e:
                                            st.error(f"Erreur lors de l'export: {e}")
                                else:
                                    st.warning("Aucun texte détecté")
                    else:
                        st.error("Erreur lors de l'analyse")

                except Exception as e:
                    st.error(f"Erreur lors de l'analyse: {e}")


# Point d'entrée pour l'exécution directe
if __name__ == "__main__":
    main()
