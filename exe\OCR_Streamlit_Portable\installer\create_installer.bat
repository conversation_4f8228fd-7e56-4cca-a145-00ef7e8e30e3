@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================================================
echo CRÉATION DE L'INSTALLATEUR OCR INTELLIGENT
echo ================================================================
echo.

:: Vérifier que Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERREUR] Python n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)

echo [INFO] Python détecté
python --version

:: Vérifier que PyInstaller est installé
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installation de PyInstaller...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo [ERREUR] Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

echo [INFO] PyInstaller détecté

:: Créer le dossier de sortie
if not exist "dist" mkdir dist
if not exist "build" mkdir build

:: Copier les fichiers nécessaires
echo [INFO] Préparation des fichiers...
if not exist "app" mkdir app
xcopy /E /I /Y "..\app.py" "app\"
xcopy /E /I /Y "..\frontend" "app\frontend\"
xcopy /E /I /Y "..\backend" "app\backend\"
xcopy /E /I /Y "..\models" "app\models\"
xcopy /E /I /Y "..\output" "app\output\"
xcopy /E /I /Y "..\README.md" "app\"

:: Créer le fichier spec pour PyInstaller
echo [INFO] Création du fichier spec...
echo # -*- mode: python ; coding: utf-8 -*- > ocr_app.spec
echo block_cipher = None >> ocr_app.spec
echo >> ocr_app.spec
echo a = Analysis(['app\app.py'], >> ocr_app.spec
echo     pathex=['app'], >> ocr_app.spec
echo     binaries=[], >> ocr_app.spec
echo     datas=[('app\frontend', 'frontend'), ('app\backend', 'backend'), ('app\models', 'models'), ('app\output', 'output')], >> ocr_app.spec
echo     hiddenimports=['streamlit', 'PIL', 'numpy', 'pandas', 'pytesseract', 'easyocr', 'doctr', 'torch', 'cv2'], >> ocr_app.spec
echo     hookspath=[], >> ocr_app.spec
echo     hooksconfig={}, >> ocr_app.spec
echo     runtime_hooks=[], >> ocr_app.spec
echo     excludes=['tkinter', 'unittest', 'test', 'tests'], >> ocr_app.spec
echo     win_no_prefer_redirects=False, >> ocr_app.spec
echo     win_private_assemblies=False, >> ocr_app.spec
echo     cipher=block_cipher, >> ocr_app.spec
echo     noarchive=False, >> ocr_app.spec
echo ) >> ocr_app.spec
echo >> ocr_app.spec
echo pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher) >> ocr_app.spec
echo >> ocr_app.spec
echo exe = EXE(pyz, >> ocr_app.spec
echo     a.scripts, >> ocr_app.spec
echo     [], >> ocr_app.spec
echo     exclude_binaries=True, >> ocr_app.spec
echo     name='OCR_Intelligent', >> ocr_app.spec
echo     debug=False, >> ocr_app.spec
echo     bootloader_ignore_signals=False, >> ocr_app.spec
echo     strip=False, >> ocr_app.spec
echo     upx=True, >> ocr_app.spec
echo     console=True, >> ocr_app.spec
echo     disable_windowed_traceback=False, >> ocr_app.spec
echo     argv_emulation=False, >> ocr_app.spec
echo     target_arch=None, >> ocr_app.spec
echo     codesign_identity=None, >> ocr_app.spec
echo     entitlements_file=None, >> ocr_app.spec
echo     icon='ocr_icon.ico', >> ocr_app.spec
echo ) >> ocr_app.spec
echo >> ocr_app.spec
echo coll = COLLECT(exe, >> ocr_app.spec
echo     a.binaries, >> ocr_app.spec
echo     a.zipfiles, >> ocr_app.spec
echo     a.datas, >> ocr_app.spec
echo     strip=False, >> ocr_app.spec
echo     upx=True, >> ocr_app.spec
echo     upx_exclude=[], >> ocr_app.spec
echo     name='OCR_Intelligent', >> ocr_app.spec
echo ) >> ocr_app.spec

:: Créer l'icône
echo [INFO] Création de l'icône...
python create_icon.py

:: Construire l'exécutable avec PyInstaller
echo [INFO] Construction de l'exécutable avec PyInstaller...
python -m PyInstaller --clean --noconfirm ocr_app.spec
if errorlevel 1 (
    echo [ERREUR] Échec de la construction PyInstaller
    pause
    exit /b 1
)

:: Vérifier si Inno Setup est installé
set INNO_SETUP_PATH=
for %%p in (
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    "C:\Program Files\Inno Setup 6\ISCC.exe"
    "C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
    "C:\Program Files\Inno Setup 5\ISCC.exe"
) do (
    if exist %%p (
        set INNO_SETUP_PATH=%%p
        goto :found_inno
    )
)

:found_inno
if not defined INNO_SETUP_PATH (
    echo [AVERTISSEMENT] Inno Setup non trouvé. L'installateur ne sera pas créé.
    echo Téléchargez Inno Setup depuis: https://jrsoftware.org/isinfo.php
    goto :skip_inno
)

:: Créer l'installateur avec Inno Setup
echo [INFO] Création de l'installateur avec Inno Setup...
"%INNO_SETUP_PATH%" ocr_setup.iss
if errorlevel 1 (
    echo [ERREUR] Échec de la création de l'installateur
    pause
    exit /b 1
)

:skip_inno

:: Vérifier les résultats
echo.
echo ================================================================
echo CONSTRUCTION TERMINÉE
echo ================================================================
echo.
echo Fichiers générés :
if exist "dist\OCR_Intelligent" (
    echo ✓ Application portable : dist\OCR_Intelligent\
)
if exist "dist\OCR_Intelligent_Setup.exe" (
    echo ✓ Installateur Windows : dist\OCR_Intelligent_Setup.exe
) else (
    echo ⚠ Installateur Windows non créé (Inno Setup requis)
)

echo.
echo Prochaines étapes :
echo 1. Tester l'application dans dist\OCR_Intelligent\
echo 2. Tester l'installateur sur une machine propre
echo 3. Distribuer l'installateur
echo.

:: Proposer d'ouvrir le dossier de sortie
set /p OPEN_FOLDER="Ouvrir le dossier de sortie ? (o/N) : "
if /i "%OPEN_FOLDER%"=="o" (
    explorer "dist"
)

echo.
echo Appuyez sur une touche pour fermer...
pause >nul
exit /b 0
