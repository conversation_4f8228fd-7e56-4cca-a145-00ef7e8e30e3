{"image_path": "images/exemple1.png", "total_zones": 1, "zones": [{"id": 0, "type": "text", "bbox": {"x1": 0, "y1": 0, "x2": 512, "y2": 739}, "layout_confidence": 1.0, "area": 378368, "content": {"lines": ["Loco Facture N° vile, le …L", "Nom de l'entreprise", "<PERSON><PERSON><PERSON>", "Ville ot Code Postal", "Numéro de téléphone", "<PERSON><PERSON>", "Nom du dient", "<PERSON><PERSON><PERSON>", "Numéro de téléphone", "<PERSON><PERSON>", "F", "-", "€", "€ heures «", "E heures «", "Total HT", "Modalités et conditions de", "réglement", "Signature", "Date d'échéance", "Mon entreprise — Socé . au capial de uc", "Nt"], "confidences": [63.833333333333336, 58.666666666666664, 67.0, 71.75, 73.33333333333333, 56.0, 76.33333333333333, 67.0, 73.33333333333333, 56.0, 48.0, 80.0, 40.0, 57.666666666666664, 66.66666666666667, 84.5, 66.5, 77.0, 87.0, 78.0, 55.0, 34.0], "avg_confidence": 65.34469696969697, "text": "Loco Facture N° vile, le …L\nNom de l'entreprise\nAdresse\nVille ot Code Postal\nNuméro de téléphone\nEmai\nNom du dient\nAdresse\nNuméro de téléphone\nEmai\nF\n-\n€\n€ heures «\nE heures «\nTotal HT\nModalités et conditions de\nréglement\nSignature\nDate d'échéance\nMon entreprise — Socé . au capial de uc\nNt", "char_count": 269, "line_count": 22}}], "full_text": "Loco Facture N° vile, le …L\nNom de l'entreprise\nAdresse\nVille ot Code Postal\nNuméro de téléphone\nEmai\nNom du dient\nAdresse\nNuméro de téléphone\nEmai\nF\n-\n€\n€ heures «\nE heures «\nTotal HT\nModalités et conditions de\nréglement\nSignature\nDate d'échéance\nMon entreprise — Socé . au capial de uc\nNt", "metadata": {"layout_detection_available": false, "processing_timestamp": 1753112395.7979398}, "stats": {"total_zones": 1, "text_zones": 1, "table_zones": 0, "title_zones": 0, "figure_zones": 0, "processing_time": 0}, "debug_image": "output\\debug\\exemple1_layout_debug.jpg"}