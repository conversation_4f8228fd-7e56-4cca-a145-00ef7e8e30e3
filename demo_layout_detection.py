#!/usr/bin/env python3
"""
Démonstration des capacités de détection de mise en page
Compare les résultats avec et sans détection de mise en page
"""
import os
import sys
import time
import json
from pathlib import Path

# Configuration de l'environnement
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"] = ""
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"

# Ajouter le répertoire courant au path
sys.path.insert(0, str(Path(__file__).parent))

def demo_comparison():
    """Démonstration comparative des deux modes"""
    print("=" * 80)
    print("DÉMONSTRATION - DÉTECTION DE MISE EN PAGE vs OCR CLASSIQUE")
    print("=" * 80)
    
    # Trouver une image de test
    test_images = [
        "images/facture1.png",
        "images/FACTURE-ARTFORDPLUS_N1-1.jpg",
        "images/exemple1.png",
        "images/exemple2.jpg"
    ]
    
    test_image = None
    for img_path in test_images:
        if Path(img_path).exists():
            test_image = img_path
            break
    
    if not test_image:
        print("[ERROR] Aucune image de test trouvée")
        return False
    
    print(f"[INFO] Image de démonstration: {test_image}")
    print()
    
    try:
        from backend.main import run_all_ocr_methods
        
        # === MODE CLASSIQUE ===
        print("🔹 MODE CLASSIQUE (OCR traditionnel)")
        print("-" * 50)
        start_time = time.time()
        
        classic_results, classic_method, classic_word = run_all_ocr_methods(
            [test_image], 
            use_layout_detection=False
        )
        
        classic_time = time.time() - start_time
        
        # Statistiques mode classique
        classic_lines = len(classic_results[classic_method]['lines'])
        classic_conf = classic_results[classic_method]['avg_conf']
        classic_text = '\n'.join(classic_results[classic_method]['lines'])
        
        print(f"⏱️  Temps de traitement: {classic_time:.2f}s")
        print(f"📄 Lignes extraites: {classic_lines}")
        print(f"🎯 Confiance moyenne: {classic_conf:.1f}%")
        print(f"📝 Caractères extraits: {len(classic_text)}")
        print(f"💾 Document généré: {classic_word}")
        print()
        
        # === MODE STRUCTURÉ ===
        print("🔸 MODE STRUCTURÉ (avec détection de mise en page)")
        print("-" * 50)
        start_time = time.time()
        
        structured_results, structured_method, structured_word = run_all_ocr_methods(
            [test_image], 
            use_layout_detection=True
        )
        
        structured_time = time.time() - start_time
        
        # Statistiques mode structuré
        structured_lines = len(structured_results[structured_method]['lines'])
        structured_conf = structured_results[structured_method]['avg_conf']
        structured_text = '\n'.join(structured_results[structured_method]['lines'])
        
        print(f"⏱️  Temps de traitement: {structured_time:.2f}s")
        print(f"📄 Lignes extraites: {structured_lines}")
        print(f"🎯 Confiance moyenne: {structured_conf:.1f}%")
        print(f"📝 Caractères extraits: {len(structured_text)}")
        print(f"💾 Document généré: {structured_word}")
        
        # Vérifier les fichiers JSON structurés
        json_files = list(Path("output").glob("*.json"))
        if json_files:
            print(f"📊 Résultats JSON: {len(json_files)} fichier(s)")
            for json_file in json_files:
                print(f"   - {json_file}")
        
        # Images de debug
        debug_images = list(Path("output/debug").glob("*_layout_debug.jpg"))
        if debug_images:
            print(f"🖼️  Images de debug: {len(debug_images)} fichier(s)")
        
        print()
        
        # === COMPARAISON ===
        print("📊 COMPARAISON DES RÉSULTATS")
        print("-" * 50)
        
        time_diff = structured_time - classic_time
        time_percent = (time_diff / classic_time) * 100 if classic_time > 0 else 0
        
        print(f"⏱️  Différence de temps: {time_diff:+.2f}s ({time_percent:+.1f}%)")
        
        if structured_lines != classic_lines:
            line_diff = structured_lines - classic_lines
            print(f"📄 Différence de lignes: {line_diff:+d}")
        else:
            print(f"📄 Même nombre de lignes: {structured_lines}")
        
        conf_diff = structured_conf - classic_conf
        print(f"🎯 Différence de confiance: {conf_diff:+.1f}%")
        
        char_diff = len(structured_text) - len(classic_text)
        print(f"📝 Différence de caractères: {char_diff:+d}")
        
        print()
        
        # === APERÇU DES TEXTES ===
        print("📖 APERÇU DES TEXTES EXTRAITS")
        print("-" * 50)
        
        print("🔹 Mode classique (100 premiers caractères):")
        print(f"   {classic_text[:100]}...")
        print()
        
        print("🔸 Mode structuré (100 premiers caractères):")
        print(f"   {structured_text[:100]}...")
        print()
        
        # === RECOMMANDATIONS ===
        print("💡 RECOMMANDATIONS")
        print("-" * 50)
        
        if structured_conf > classic_conf + 5:
            print("✅ Le mode structuré offre une meilleure confiance")
        elif classic_conf > structured_conf + 5:
            print("⚠️  Le mode classique semble plus fiable pour cette image")
        else:
            print("🔄 Les deux modes donnent des résultats similaires")
        
        if time_percent > 50:
            print("⚠️  Le mode structuré est significativement plus lent")
        elif time_percent > 20:
            print("ℹ️  Le mode structuré prend un peu plus de temps")
        else:
            print("✅ Temps de traitement acceptable")
        
        if len(debug_images) > 0:
            print(f"🖼️  Consultez les images de debug dans output/debug/ pour voir les zones détectées")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Erreur lors de la démonstration: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_json_analysis():
    """Analyse des résultats JSON structurés"""
    print("\n" + "=" * 80)
    print("ANALYSE DES RÉSULTATS JSON STRUCTURÉS")
    print("=" * 80)
    
    json_files = list(Path("output").glob("structured_result_*.json"))
    
    if not json_files:
        print("[INFO] Aucun fichier JSON structuré trouvé")
        print("Exécutez d'abord la démonstration comparative")
        return False
    
    for json_file in json_files:
        print(f"\n📄 Analyse de: {json_file}")
        print("-" * 50)
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Informations générales
            print(f"🖼️  Image source: {data.get('image_path', 'N/A')}")
            print(f"🔢 Zones détectées: {data.get('total_zones', 0)}")
            
            # Statistiques par type
            stats = data.get('stats', {})
            if stats:
                print("📊 Répartition des zones:")
                for zone_type in ['text_zones', 'title_zones', 'table_zones', 'figure_zones']:
                    count = stats.get(zone_type, 0)
                    if count > 0:
                        type_name = zone_type.replace('_zones', '').title()
                        print(f"   - {type_name}: {count}")
            
            # Détail des zones
            zones = data.get('zones', [])
            if zones:
                print(f"\n🔍 Détail des zones ({len(zones)}):")
                for zone in zones[:5]:  # Limiter à 5 zones pour l'affichage
                    zone_type = zone.get('type', 'unknown')
                    layout_conf = zone.get('layout_confidence', 0)
                    ocr_conf = zone.get('content', {}).get('avg_confidence', 0)
                    text_preview = zone.get('content', {}).get('text', '')[:50]
                    
                    print(f"   Zone {zone['id']}: {zone_type}")
                    print(f"     Confiance détection: {layout_conf:.2f}")
                    print(f"     Confiance OCR: {ocr_conf:.1f}%")
                    print(f"     Aperçu: {text_preview}...")
                    print()
                
                if len(zones) > 5:
                    print(f"   ... et {len(zones) - 5} autres zones")
            
            # Texte complet
            full_text = data.get('full_text', '')
            if full_text:
                print(f"📝 Texte complet: {len(full_text)} caractères")
                print(f"   Aperçu: {full_text[:100]}...")
            
        except Exception as e:
            print(f"[ERROR] Erreur lors de l'analyse de {json_file}: {e}")
    
    return True

def main():
    """Fonction principale de démonstration"""
    print("DÉMONSTRATION COMPLÈTE - DÉTECTION DE MISE EN PAGE")
    print("=" * 80)
    print("Cette démonstration compare les modes classique et structuré")
    print("et analyse les résultats JSON générés.")
    print()
    
    # Créer les dossiers nécessaires
    Path("output").mkdir(exist_ok=True)
    Path("output/debug").mkdir(exist_ok=True)
    
    # Démonstration comparative
    success1 = demo_comparison()
    
    if success1:
        # Analyse des JSON
        success2 = demo_json_analysis()
        
        print("\n" + "=" * 80)
        print("🎉 DÉMONSTRATION TERMINÉE")
        print("=" * 80)
        print()
        print("📁 Fichiers générés:")
        print("   - Documents Word dans output/")
        print("   - Résultats JSON dans output/")
        print("   - Images de debug dans output/debug/")
        print()
        print("💡 Pour utiliser la détection de mise en page:")
        print("   1. Lancez: python main.py")
        print("   2. Cochez: '[BETA] Activer la détection automatique de mise en page'")
        print("   3. Téléversez votre document")
        print()
        print("📖 Consultez LAYOUT_DETECTION_GUIDE.md pour plus d'informations")
        
    else:
        print("\n❌ La démonstration a échoué")
        print("Vérifiez que les dépendances sont installées:")
        print("   pip install -r requirements.txt")

if __name__ == "__main__":
    main()
