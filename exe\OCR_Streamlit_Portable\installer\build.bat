@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================================================
echo CONSTRUCTION DE L'INSTALLATEUR OCR INTELLIGENT
echo ================================================================
echo.

:: Vérifier que Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERREUR] Python n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)

echo [INFO] Python détecté
python --version

:: Vérifier que PyInstaller est installé
python -m PyInstaller --version >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installation de PyInstaller...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo [ERREUR] Impossible d'installer PyInstaller
        pause
        exit /b 1
    )
)

echo [INFO] PyInstaller détecté
python -m PyInstaller --version

:: Vérifier que Pillow est installé pour la création d'icône
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installation de Pillow pour la création d'icône...
    python -m pip install Pillow
    if errorlevel 1 (
        echo [ERREUR] Impossible d'installer Pillow
        pause
        exit /b 1
    )
)

echo [INFO] Pillow détecté

:: Créer l'icône de l'application
echo.
echo [ÉTAPE] Création de l'icône de l'application...
python create_icon.py
if errorlevel 1 (
    echo [AVERTISSEMENT] Impossible de créer l'icône
)

:: Lancer la construction
echo.
echo [ÉTAPE] Lancement de la construction...
python build_installer.py
set BUILD_RESULT=!errorlevel!

:: Vérifier le résultat
echo.
if !BUILD_RESULT! equ 0 (
    echo ================================================================
    echo CONSTRUCTION TERMINÉE AVEC SUCCÈS
    echo ================================================================
    echo.
    echo Fichiers générés :
    if exist "dist\OCR_Intelligent_Setup.exe" (
        echo ✓ Installateur Windows : dist\OCR_Intelligent_Setup.exe
        for %%I in ("dist\OCR_Intelligent_Setup.exe") do echo   Taille : %%~zI octets
    ) else (
        echo ⚠ Installateur Windows non trouvé (Inno Setup requis)
    )
    
    if exist "dist\OCR_Intelligent" (
        echo ✓ Application portable : dist\OCR_Intelligent\
    )
    
    echo.
    echo Prochaines étapes :
    echo 1. Tester l'application dans dist\OCR_Intelligent\
    echo 2. Tester l'installateur sur une machine propre
    echo 3. Distribuer l'installateur
    echo.
    
    :: Proposer d'ouvrir le dossier de sortie
    set /p OPEN_FOLDER="Ouvrir le dossier de sortie ? (o/N) : "
    if /i "!OPEN_FOLDER!"=="o" (
        explorer "dist"
    )
    
) else (
    echo ================================================================
    echo ÉCHEC DE LA CONSTRUCTION
    echo ================================================================
    echo.
    echo Vérifiez les erreurs ci-dessus et :
    echo 1. Assurez-vous que toutes les dépendances sont installées
    echo 2. Vérifiez que l'application fonctionne correctement
    echo 3. Consultez les logs d'erreur
    echo.
)

echo.
echo Appuyez sur une touche pour fermer...
pause >nul
exit /b !BUILD_RESULT!
