#!/usr/bin/env python3
"""
Script pour télécharger et installer Inno Setup
"""
import os
import sys
import subprocess
import urllib.request
import tempfile
from pathlib import Path

def print_header(title):
    """Affiche un en-tête"""
    print(f"\n{'='*60}")
    print(f"{title}")
    print(f"{'='*60}")

def print_step(message):
    """Affiche une étape"""
    print(f"\n[STEP] {message}")

def print_info(message):
    """Affiche une information"""
    print(f"[INFO] {message}")

def print_success(message):
    """Affiche un succès"""
    print(f"[OK] {message}")

def print_error(message):
    """Affiche une erreur"""
    print(f"[ERROR] {message}")

def print_warning(message):
    """Affiche un avertissement"""
    print(f"[WARNING] {message}")

def check_inno_setup_installed():
    """Vérifie si Inno Setup est déjà installé"""
    print_step("Vérification de l'installation d'Inno Setup")
    
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]
    
    for path in inno_paths:
        if Path(path).exists():
            print_success(f"Inno Setup trouvé: {path}")
            return True, path
    
    print_info("Inno Setup non trouvé")
    return False, None

def download_inno_setup():
    """Télécharge Inno Setup"""
    print_step("Téléchargement d'Inno Setup")
    
    # URL de téléchargement d'Inno Setup 6
    download_url = "https://jrsoftware.org/download.php/is.exe"
    
    try:
        # Créer un fichier temporaire
        temp_dir = tempfile.gettempdir()
        installer_path = Path(temp_dir) / "innosetup-installer.exe"
        
        print_info(f"Téléchargement depuis: {download_url}")
        print_info(f"Destination: {installer_path}")
        
        # Télécharger le fichier
        def show_progress(block_num, block_size, total_size):
            if total_size > 0:
                percent = min(100, (block_num * block_size * 100) // total_size)
                print(f"\rTéléchargement: {percent}%", end="", flush=True)
        
        urllib.request.urlretrieve(download_url, installer_path, show_progress)
        print()  # Nouvelle ligne après la barre de progression
        
        if installer_path.exists() and installer_path.stat().st_size > 0:
            print_success(f"Téléchargement réussi: {installer_path}")
            return str(installer_path)
        else:
            print_error("Échec du téléchargement")
            return None
            
    except Exception as e:
        print_error(f"Erreur lors du téléchargement: {e}")
        return None

def install_inno_setup(installer_path):
    """Installe Inno Setup"""
    print_step("Installation d'Inno Setup")
    
    try:
        print_info("Lancement de l'installateur Inno Setup...")
        print_info("Suivez les instructions à l'écran pour installer Inno Setup")
        print_info("Utilisez les options par défaut recommandées")
        
        # Lancer l'installateur
        result = subprocess.run([installer_path], check=False)
        
        if result.returncode == 0:
            print_success("Installation d'Inno Setup terminée")
            return True
        else:
            print_warning(f"L'installateur s'est terminé avec le code: {result.returncode}")
            print_info("Cela peut être normal si l'utilisateur a annulé ou si c'était déjà installé")
            return True
            
    except Exception as e:
        print_error(f"Erreur lors de l'installation: {e}")
        return False

def verify_installation():
    """Vérifie que l'installation a réussi"""
    print_step("Vérification de l'installation")
    
    # Attendre un peu pour que l'installation se termine
    import time
    time.sleep(2)
    
    installed, path = check_inno_setup_installed()
    
    if installed:
        print_success(f"Inno Setup correctement installé: {path}")
        
        # Tester la compilation
        try:
            result = subprocess.run([path, "/?"], capture_output=True, text=True, timeout=10)
            if "Inno Setup" in result.stdout or "Inno Setup" in result.stderr:
                print_success("Inno Setup fonctionne correctement")
                return True
        except:
            pass
        
        print_success("Inno Setup installé (test de fonctionnement non concluant)")
        return True
    else:
        print_error("Inno Setup n'a pas été installé correctement")
        return False

def cleanup_installer(installer_path):
    """Nettoie le fichier d'installation temporaire"""
    try:
        if installer_path and Path(installer_path).exists():
            Path(installer_path).unlink()
            print_info("Fichier d'installation temporaire supprimé")
    except Exception as e:
        print_warning(f"Impossible de supprimer le fichier temporaire: {e}")

def main():
    """Fonction principale"""
    print_header("INSTALLATION D'INNO SETUP")
    
    # Vérifier si déjà installé
    installed, path = check_inno_setup_installed()
    
    if installed:
        print_success("Inno Setup est déjà installé")
        print_info("Aucune action nécessaire")
        return 0
    
    # Demander confirmation
    print_info("Inno Setup n'est pas installé")
    print_info("Ce script va télécharger et installer Inno Setup 6")
    print_info("Taille approximative: 2-3 MB")
    print()
    
    response = input("Continuer avec l'installation ? (o/N): ").strip().lower()
    if response not in ['o', 'oui', 'y', 'yes']:
        print_info("Installation annulée par l'utilisateur")
        return 1
    
    # Télécharger Inno Setup
    installer_path = download_inno_setup()
    
    if not installer_path:
        print_error("Impossible de télécharger Inno Setup")
        print_info("Installation manuelle requise:")
        print_info("1. Allez sur https://jrsoftware.org/isinfo.php")
        print_info("2. Téléchargez Inno Setup 6")
        print_info("3. Installez-le avec les options par défaut")
        return 1
    
    try:
        # Installer Inno Setup
        if install_inno_setup(installer_path):
            # Vérifier l'installation
            if verify_installation():
                print_header("INSTALLATION RÉUSSIE")
                print_success("Inno Setup a été installé avec succès")
                print_info("Vous pouvez maintenant créer l'installateur OCR")
                return 0
            else:
                print_error("L'installation semble avoir échoué")
                return 1
        else:
            print_error("Échec de l'installation")
            return 1
            
    finally:
        # Nettoyer le fichier temporaire
        cleanup_installer(installer_path)

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nInstallation interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"Erreur inattendue: {e}")
        sys.exit(1)
