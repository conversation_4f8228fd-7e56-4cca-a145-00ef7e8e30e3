# OCR Intelligent - Git Ignore File
# Prevents temporary and generated files from being committed

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Build and distribution
build/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.env

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~
.spyderproject
.spyproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# OCR Intelligent specific working directories
output/*.txt
output/*.docx
output/*.xlsx
output/*.pdf
output/*.json
output/debug/*.jpg
output/debug/*.png
output/backend/
logs/*.log
logs/*.txt
corrected/*.txt
corrected/*.docx

# Temporary and backup files
*.tmp
*.temp
*.bak
*.old
*.orig
*~

# Build artifacts (keep source installer script)
dist/*.exe
dist/*.msi

# Test and debug files
test_*.py
*_test.py
debug_*.bat
*_debug.bat
demo_*.py
*_demo.py
test_final_validation.py
test_layout_detection.py

# Jupyter Notebook
.ipynb_checkpoints

# PyTorch model cache
.cache/

# Streamlit
.streamlit/

# Local configuration overrides
config_local.py
settings_local.py

# Model files (keep essential models, ignore large training files)
correction_model/
*.safetensors
*.bin

# LayoutParser specific
.layoutparser/
layoutparser_cache/
*.pth