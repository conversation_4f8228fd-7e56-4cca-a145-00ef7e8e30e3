#!/usr/bin/env python3
"""
Script de construction de l'installateur Windows pour l'application OCR Streamlit
"""
import os
import sys
import shutil
import subprocess
import json
from pathlib import Path
import zipfile
import tempfile

class OCRInstallerBuilder:
    """Constructeur d'installateur pour l'application OCR"""
    
    def __init__(self):
        self.root_dir = Path(__file__).parent.parent
        self.installer_dir = Path(__file__).parent
        self.build_dir = self.installer_dir / "build"
        self.dist_dir = self.installer_dir / "dist"
        self.temp_dir = None
        
        # Configuration de l'application
        self.app_config = {
            "name": "OCR Intelligent",
            "version": "1.0.0",
            "description": "Application OCR avec Tesseract, EasyOCR et DocTR",
            "author": "Votre Organisation",
            "url": "https://votre-site.com",
            "icon": "ocr_icon.ico",
            "main_script": "app.py"
        }
    
    def print_step(self, message):
        """Affiche une étape"""
        print(f"\n{'='*60}")
        print(f"[STEP] {message}")
        print(f"{'='*60}")
    
    def print_info(self, message):
        """Affiche une information"""
        print(f"[INFO] {message}")
    
    def print_success(self, message):
        """Affiche un succès"""
        print(f"[OK] {message}")
    
    def print_error(self, message):
        """Affiche une erreur"""
        print(f"[ERROR] {message}")
    
    def print_warning(self, message):
        """Affiche un avertissement"""
        print(f"[WARNING] {message}")
    
    def setup_directories(self):
        """Configure les répertoires de construction"""
        self.print_step("Configuration des répertoires")
        
        # Nettoyer les répertoires existants
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        
        # Créer les répertoires
        self.build_dir.mkdir(parents=True, exist_ok=True)
        self.dist_dir.mkdir(parents=True, exist_ok=True)
        
        # Créer un répertoire temporaire
        self.temp_dir = Path(tempfile.mkdtemp())
        
        self.print_success(f"Répertoires configurés:")
        self.print_info(f"  Build: {self.build_dir}")
        self.print_info(f"  Dist: {self.dist_dir}")
        self.print_info(f"  Temp: {self.temp_dir}")
    
    def prepare_application(self):
        """Prépare l'application pour l'empaquetage"""
        self.print_step("Préparation de l'application")
        
        # Copier les fichiers de l'application
        app_dir = self.temp_dir / "app"
        app_dir.mkdir(exist_ok=True)
        
        # Fichiers à copier
        files_to_copy = [
            "app.py",
            "requirements.txt",
            "backend/",
            "frontend/",
            "models/",
            "README.md"
        ]
        
        for item in files_to_copy:
            src = self.root_dir / item
            dst = app_dir / item
            
            if src.exists():
                if src.is_file():
                    dst.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(src, dst)
                    self.print_info(f"Copié: {item}")
                elif src.is_dir():
                    shutil.copytree(src, dst, dirs_exist_ok=True)
                    self.print_info(f"Copié dossier: {item}")
            else:
                self.print_warning(f"Fichier manquant: {item}")
        
        # Créer le script de lancement principal
        self.create_main_launcher(app_dir)
        
        # Créer les fichiers de configuration
        self.create_config_files(app_dir)
        
        self.print_success("Application préparée")
        return app_dir
    
    def create_main_launcher(self, app_dir):
        """Crée le script de lancement principal"""
        launcher_content = '''#!/usr/bin/env python3
"""
Lanceur principal pour l'application OCR Intelligent
"""
import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def setup_environment():
    """Configure l'environnement d'exécution"""
    # Ajouter le répertoire de l'application au PATH Python
    app_dir = Path(__file__).parent
    sys.path.insert(0, str(app_dir))
    
    # Configurer les variables d'environnement
    os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
    os.environ['STREAMLIT_SERVER_PORT'] = '8501'
    os.environ['STREAMLIT_BROWSER_GATHER_USAGE_STATS'] = 'false'

def launch_streamlit():
    """Lance l'application Streamlit"""
    try:
        print("Démarrage de l'application OCR Intelligent...")
        print("L'application sera accessible sur http://localhost:8501")
        
        # Lancer Streamlit
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", "8501",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ])
        
        # Attendre un peu puis ouvrir le navigateur
        time.sleep(3)
        webbrowser.open("http://localhost:8501")
        
        # Attendre que le processus se termine
        process.wait()
        
    except KeyboardInterrupt:
        print("\\nApplication arrêtée par l'utilisateur")
    except Exception as e:
        print(f"Erreur lors du lancement: {e}")
        input("Appuyez sur Entrée pour fermer...")

def main():
    """Fonction principale"""
    setup_environment()
    launch_streamlit()

if __name__ == "__main__":
    main()
'''
        
        launcher_path = app_dir / "ocr_launcher.py"
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        
        self.print_info("Script de lancement créé")
    
    def create_config_files(self, app_dir):
        """Crée les fichiers de configuration"""
        
        # Fichier de configuration de l'application
        config = {
            "app": self.app_config,
            "paths": {
                "models": "models",
                "output": "output",
                "temp": "temp"
            },
            "ocr": {
                "default_engine": "tesseract",
                "languages": ["fr", "en"],
                "confidence_threshold": 30
            }
        }
        
        config_path = app_dir / "config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        self.print_info("Fichiers de configuration créés")
    
    def create_pyinstaller_spec(self, app_dir):
        """Crée le fichier spec pour PyInstaller"""
        self.print_step("Création du fichier PyInstaller spec")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# Configuration
app_dir = Path(r"{app_dir}")
block_cipher = None

# Données à inclure
datas = [
    (str(app_dir / "backend"), "backend"),
    (str(app_dir / "frontend"), "frontend"),
    (str(app_dir / "models"), "models"),
    (str(app_dir / "config.json"), "."),
    (str(app_dir / "README.md"), "."),
]

# Modules cachés nécessaires
hiddenimports = [
    'streamlit',
    'PIL',
    'numpy',
    'pandas',
    'pytesseract',
    'easyocr',
    'doctr',
    'torch',
    'torchvision',
    'cv2',
    'openpyxl',
    'python-docx',
    'reportlab',
    'matplotlib',
    'plotly',
    'altair',
    'streamlit.web.cli',
    'streamlit.runtime.scriptrunner.script_runner',
]

# Exclusions pour réduire la taille
excludes = [
    'tkinter',
    'unittest',
    'test',
    'tests',
    'pytest',
    'setuptools',
    'distutils',
]

a = Analysis(
    [str(app_dir / "ocr_launcher.py")],
    pathex=[str(app_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='{self.app_config["name"].replace(" ", "_")}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='{self.app_config["name"].replace(" ", "_")}',
)
'''
        
        spec_path = self.build_dir / "ocr_app.spec"
        with open(spec_path, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        self.print_success(f"Fichier spec créé: {spec_path}")
        return spec_path
    
    def build_executable(self, spec_path):
        """Construit l'exécutable avec PyInstaller"""
        self.print_step("Construction de l'exécutable avec PyInstaller")
        
        try:
            # Exécuter PyInstaller
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                "--distpath", str(self.dist_dir),
                "--workpath", str(self.build_dir / "work"),
                str(spec_path)
            ]
            
            self.print_info(f"Commande: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.print_success("Exécutable construit avec succès")
                return True
            else:
                self.print_error(f"Erreur PyInstaller: {result.stderr}")
                return False
                
        except Exception as e:
            self.print_error(f"Erreur lors de la construction: {e}")
            return False
    
    def cleanup(self):
        """Nettoie les fichiers temporaires"""
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            self.print_info("Fichiers temporaires nettoyés")
    
    def build(self):
        """Construit l'installateur complet"""
        try:
            self.print_step("CONSTRUCTION DE L'INSTALLATEUR OCR")
            
            # 1. Configuration des répertoires
            self.setup_directories()
            
            # 2. Préparation de l'application
            app_dir = self.prepare_application()
            
            # 3. Création du fichier spec PyInstaller
            spec_path = self.create_pyinstaller_spec(app_dir)
            
            # 4. Construction de l'exécutable
            if self.build_executable(spec_path):
                self.print_step("CONSTRUCTION RÉUSSIE")
                self.print_success(f"Exécutable disponible dans: {self.dist_dir}")
                return True
            else:
                self.print_error("Échec de la construction")
                return False
                
        except Exception as e:
            self.print_error(f"Erreur lors de la construction: {e}")
            return False
        finally:
            self.cleanup()

    def create_inno_setup_installer(self):
        """Crée l'installateur Windows avec Inno Setup"""
        self.print_step("Création de l'installateur Windows")

        # Vérifier que l'exécutable existe
        exe_dir = self.dist_dir / self.app_config["name"].replace(" ", "_")
        if not exe_dir.exists():
            self.print_error(f"Répertoire exécutable manquant: {exe_dir}")
            return False

        # Créer l'icône
        self.create_application_icon()

        # Chemin du script Inno Setup
        iss_path = self.installer_dir / "ocr_setup.iss"
        if not iss_path.exists():
            self.print_error(f"Script Inno Setup manquant: {iss_path}")
            return False

        try:
            # Chercher Inno Setup
            inno_setup_paths = [
                r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
                r"C:\Program Files\Inno Setup 6\ISCC.exe",
                r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
                r"C:\Program Files\Inno Setup 5\ISCC.exe"
            ]

            inno_setup_exe = None
            for path in inno_setup_paths:
                if Path(path).exists():
                    inno_setup_exe = path
                    break

            if not inno_setup_exe:
                self.print_warning("Inno Setup non trouvé. Installateur non créé.")
                self.print_info("Téléchargez Inno Setup depuis: https://jrsoftware.org/isinfo.php")
                return False

            # Exécuter Inno Setup
            cmd = [inno_setup_exe, str(iss_path)]
            self.print_info(f"Commande: {' '.join(cmd)}")

            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.print_success("Installateur Windows créé avec succès")
                return True
            else:
                self.print_error(f"Erreur Inno Setup: {result.stderr}")
                return False

        except Exception as e:
            self.print_error(f"Erreur lors de la création de l'installateur: {e}")
            return False

    def create_application_icon(self):
        """Crée l'icône de l'application"""
        try:
            icon_script = self.installer_dir / "create_icon.py"
            if icon_script.exists():
                subprocess.run([sys.executable, str(icon_script)], check=True)
                self.print_info("Icône de l'application créée")
            else:
                self.print_warning("Script de création d'icône manquant")
        except Exception as e:
            self.print_warning(f"Impossible de créer l'icône: {e}")

def main():
    """Fonction principale"""
    builder = OCRInstallerBuilder()

    # Construction de l'exécutable
    exe_success = builder.build()

    if exe_success:
        # Création de l'installateur Windows
        installer_success = builder.create_inno_setup_installer()

        print("\\n" + "="*60)
        if exe_success and installer_success:
            print("CONSTRUCTION COMPLÈTE RÉUSSIE")
            print("="*60)
            print("Fichiers générés:")
            print(f"- Exécutable: {builder.dist_dir}")
            print(f"- Installateur: {builder.installer_dir / 'dist'}")
            print("\\nProchaines étapes:")
            print("1. Tester l'exécutable")
            print("2. Tester l'installateur sur différentes machines")
            print("3. Distribuer l'installateur")
        else:
            print("CONSTRUCTION PARTIELLE")
            print("="*60)
            if exe_success:
                print("✓ Exécutable créé avec succès")
            else:
                print("✗ Échec création exécutable")

            if installer_success:
                print("✓ Installateur créé avec succès")
            else:
                print("✗ Échec création installateur (Inno Setup requis)")
    else:
        print("\\n" + "="*60)
        print("ÉCHEC DE LA CONSTRUCTION")
        print("="*60)
        print("Vérifiez les erreurs ci-dessus et corrigez les problèmes")

    return 0 if exe_success else 1

if __name__ == "__main__":
    sys.exit(main())
