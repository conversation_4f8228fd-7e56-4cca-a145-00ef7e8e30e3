from docx import Document
from docx.shared import RGBColor
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import os
import tempfile
from PIL import Image
import io
import json
from typing import List, Dict, Any


def validate_and_convert_image(image_path):
    """
    Valide et convertit une image pour qu'elle soit compatible avec python-docx

    Args:
        image_path (str): Chemin vers l'image à valider

    Returns:
        str: Chemin vers l'image convertie (ou originale si compatible)
        None: Si l'image ne peut pas être traitée
    """
    if not os.path.exists(image_path):
        print(f"[WARNING] Image non trouvée: {image_path}")
        return None

    try:
        with Image.open(image_path) as img:
            # Vérifier le format et le mode de l'image
            print(f"[IMAGE] Image détectée: {img.format} {img.mode} {img.size}")

            # Formats supportés par python-docx : JPEG, PNG, BMP, TIFF
            supported_formats = ['JPEG', 'PNG', 'BMP', 'TIFF']

            # Modes problématiques qui nécessitent une conversion
            problematic_modes = ['CMYK', 'P', 'L', 'LA']

            # Si l'image est déjà dans un format supporté et un mode compatible
            if (img.format in supported_formats and
                img.mode not in problematic_modes and
                img.mode in ['RGB', 'RGBA']):
                print(f"[OK] Image compatible: {img.format} {img.mode}")
                return image_path

            # Conversion nécessaire
            print(f"[CONVERT] Conversion nécessaire: {img.format} {img.mode} → PNG RGB")

            # Convertir l'image en mode RGB
            if img.mode in ['CMYK', 'L', 'LA']:
                # Conversion spéciale pour CMYK
                if img.mode == 'CMYK':
                    img = img.convert('RGB')
                    print("   CMYK → RGB")
                # Conversion pour niveaux de gris
                elif img.mode in ['L', 'LA']:
                    img = img.convert('RGB')
                    print("   Niveaux de gris → RGB")
            elif img.mode == 'P':
                # Conversion pour palette
                img = img.convert('RGBA')
                print("   Palette → RGBA")
            elif img.mode not in ['RGB', 'RGBA']:
                # Conversion générique vers RGB
                img = img.convert('RGB')
                print(f"   {img.mode} → RGB")

            # Créer un fichier temporaire pour l'image convertie
            temp_dir = tempfile.gettempdir()
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            temp_path = os.path.join(temp_dir, f"{base_name}_converted.png")

            # Sauvegarder l'image convertie
            img.save(temp_path, 'PNG', optimize=True)
            print(f"[OK] Image convertie sauvegardée: {temp_path}")

            return temp_path

    except Exception as e:
        print(f"[ERROR] Erreur lors de la validation/conversion de l'image: {e}")
        return None


def add_image_to_document(doc, image_path, width_inches=5.5):
    """
    Ajoute une image au document Word de manière sécurisée

    Args:
        doc: Document Word
        image_path (str): Chemin vers l'image
        width_inches (float): Largeur de l'image en pouces

    Returns:
        bool: True si l'image a été ajoutée avec succès, False sinon
    """
    if not image_path or not os.path.exists(image_path):
        print("[WARNING] Aucune image à ajouter ou image inexistante")
        return False

    try:
        # Valider et convertir l'image si nécessaire
        processed_image_path = validate_and_convert_image(image_path)

        if processed_image_path is None:
            print("[ERROR] Impossible de traiter l'image pour l'export Word")
            return False

        # Ajouter l'image au document
        doc.add_picture(processed_image_path, width=Inches(width_inches))
        print(f"[OK] Image ajoutée au document Word: {os.path.basename(image_path)}")

        # Nettoyer le fichier temporaire si c'était une conversion
        if processed_image_path != image_path and os.path.exists(processed_image_path):
            try:
                os.remove(processed_image_path)
                print("[CLEAN] Fichier temporaire nettoyé")
            except Exception as e:
                print(f"[WARNING] Impossible de supprimer le fichier temporaire: {e}")

        return True

    except Exception as e:
        print(f"[ERROR] Erreur lors de l'ajout de l'image au document: {e}")
        return False


def export_to_word(text_lines, confidences, output_path, image_path=None, method=None):
    """
    Exporte les résultats OCR vers un document Word avec gestion robuste des images

    Args:
        text_lines (list): Lignes de texte extraites par OCR
        confidences (list): Scores de confiance correspondants
        output_path (str): Chemin de sortie du fichier Word
        image_path (str, optional): Chemin vers l'image source
        method (str, optional): Méthode OCR utilisée
    """
    print(f"[EXPORT] Export Word vers: {output_path}")

    try:
        doc = Document()
        doc.add_heading('Résultat OCR', 0)

        # Ajouter des informations sur la méthode utilisée
        if method:
            info_paragraph = doc.add_paragraph()
            info_run = info_paragraph.add_run(f"Méthode OCR utilisée: {method.upper()}")
            info_run.bold = True
            doc.add_paragraph()  # Ligne vide

        # Ajouter l'image de manière sécurisée
        image_added = False
        if image_path is not None:
            print(f"[IMAGE] Tentative d'ajout de l'image: {image_path}")
            image_added = add_image_to_document(doc, image_path, width_inches=5.5)

            if image_added:
                doc.add_paragraph()  # Ligne vide après l'image

        # Ajouter le texte extrait
        if image_added:
            doc.add_paragraph("Texte extrait de l'image :")
        else:
            doc.add_paragraph("Texte extrait :")

        doc.add_paragraph()  # Ligne vide

        # Ajouter les lignes de texte avec coloration selon la confiance
        if text_lines:
            for line, conf in zip(text_lines, confidences):
                if line.strip():  # Ignorer les lignes vides
                    p = doc.add_paragraph()
                    run = p.add_run(line)

                    # Coloration selon le niveau de confiance
                    if conf < 50:
                        run.font.color.rgb = RGBColor(255, 0, 0)    # Rouge pour très faible confiance
                    elif conf < 70:
                        run.font.color.rgb = RGBColor(255, 165, 0)  # Orange pour faible confiance
                    elif conf < 85:
                        run.font.color.rgb = RGBColor(255, 255, 0)  # Jaune pour confiance moyenne
                    # Pas de coloration pour confiance élevée (>= 85)
        else:
            # Aucun texte détecté
            p = doc.add_paragraph()
            run = p.add_run("Aucun texte détecté dans l'image.")
            run.font.color.rgb = RGBColor(128, 128, 128)  # Gris

        # Ajouter des statistiques de confiance
        if confidences and len(confidences) > 0:
            doc.add_paragraph()  # Ligne vide
            stats_paragraph = doc.add_paragraph()
            avg_conf = sum(confidences) / len(confidences)
            min_conf = min(confidences)
            max_conf = max(confidences)

            stats_text = f"Statistiques de confiance - Moyenne: {avg_conf:.1f}%, Min: {min_conf:.1f}%, Max: {max_conf:.1f}%"
            stats_run = stats_paragraph.add_run(stats_text)
            stats_run.italic = True
            stats_run.font.color.rgb = RGBColor(100, 100, 100)

        # Sauvegarder le document
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        doc.save(output_path)
        print(f"[OK] Document Word exporté avec succès: {output_path}")

        return True

    except Exception as e:
        print(f"[ERROR] Erreur lors de l'export Word: {e}")
        import traceback
        traceback.print_exc()
        return False


def export_structured_to_word(structured_results: List[Dict[str, Any]], output_path: str, image_path: str = None) -> bool:
    """
    Exporte les résultats OCR structurés vers un document Word avec mise en forme avancée

    Args:
        structured_results: Liste des résultats structurés par image
        output_path: Chemin de sortie du document Word
        image_path: Chemin vers l'image source (optionnel)

    Returns:
        True si l'export a réussi, False sinon
    """
    try:
        print(f"[EXPORT] Début de l'export structuré vers: {output_path}")

        # Créer un nouveau document
        doc = Document()

        # Ajouter un titre principal
        title = doc.add_heading('Résultats OCR avec Détection de Mise en Page', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Ajouter l'image source si disponible
        if image_path and os.path.exists(image_path):
            doc.add_heading('Image Source', level=1)
            if add_image_to_document(doc, image_path, width_inches=6):
                doc.add_paragraph()  # Espacement

        # Traiter chaque résultat structuré (une image)
        for img_idx, result in enumerate(structured_results):
            if len(structured_results) > 1:
                doc.add_heading(f'Image {img_idx + 1}', level=1)

            # Informations générales
            doc.add_heading('Informations Générales', level=2)
            info_para = doc.add_paragraph()
            info_para.add_run(f"Zones détectées: ").bold = True
            info_para.add_run(f"{result.get('total_zones', 0)}\n")

            # Statistiques par type de zone
            stats = result.get('stats', {})
            if stats:
                stats_para = doc.add_paragraph()
                stats_para.add_run("Répartition des zones:\n").bold = True
                for zone_type in ['text_zones', 'title_zones', 'table_zones', 'figure_zones']:
                    count = stats.get(zone_type, 0)
                    if count > 0:
                        type_name = zone_type.replace('_zones', '').title()
                        stats_para.add_run(f"  • {type_name}: {count}\n")

            # Traiter chaque zone
            zones = result.get('zones', [])
            if zones:
                doc.add_heading('Contenu par Zone', level=2)

                for zone in zones:
                    zone_type = zone.get('type', 'unknown')
                    zone_id = zone.get('id', 0)
                    content = zone.get('content', {})

                    # Titre de la zone avec style selon le type
                    zone_title = f"Zone {zone_id + 1}: {zone_type.title()}"
                    zone_heading = doc.add_heading(zone_title, level=3)

                    # Informations de la zone
                    zone_info = doc.add_paragraph()
                    zone_info.add_run("Confiance de détection: ").bold = True
                    zone_info.add_run(f"{zone.get('layout_confidence', 0):.2f}\n")
                    zone_info.add_run("Confiance OCR moyenne: ").bold = True
                    zone_info.add_run(f"{content.get('avg_confidence', 0):.2f}\n")

                    # Contenu de la zone avec style selon le type
                    zone_text = content.get('text', '').strip()
                    if zone_text:
                        if zone_type == 'title':
                            # Titre avec style spécial
                            title_para = doc.add_paragraph()
                            title_run = title_para.add_run(zone_text)
                            title_run.bold = True
                            title_run.font.size = Inches(0.2)  # Plus grand
                            title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        elif zone_type == 'table':
                            # Tableau avec bordure
                            table_para = doc.add_paragraph()
                            table_run = table_para.add_run(f"[TABLEAU]\n{zone_text}\n[/TABLEAU]")
                            table_run.font.color.rgb = RGBColor(0, 100, 0)  # Vert foncé

                        elif zone_type == 'figure':
                            # Figure avec style italique
                            figure_para = doc.add_paragraph()
                            figure_run = figure_para.add_run(f"[FIGURE: {zone_text}]")
                            figure_run.italic = True
                            figure_run.font.color.rgb = RGBColor(100, 100, 100)  # Gris

                        else:  # text, list, ou autre
                            # Texte normal
                            doc.add_paragraph(zone_text)

                    # Espacement entre les zones
                    doc.add_paragraph()

            # Texte complet reconstruit
            full_text = result.get('full_text', '').strip()
            if full_text:
                doc.add_heading('Texte Complet Reconstruit', level=2)
                doc.add_paragraph(full_text)

        # Informations techniques en fin de document
        doc.add_page_break()
        doc.add_heading('Informations Techniques', level=1)

        tech_para = doc.add_paragraph()
        tech_para.add_run("Méthode: ").bold = True
        tech_para.add_run("OCR Structuré avec Détection de Mise en Page\n")
        tech_para.add_run("Modèle de détection: ").bold = True
        tech_para.add_run("PubLayNet (LayoutParser)\n")
        tech_para.add_run("Moteur OCR: ").bold = True
        tech_para.add_run("Tesseract\n")

        # Sauvegarder le document
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        doc.save(output_path)
        print(f"[OK] Document Word structuré exporté avec succès: {output_path}")

        return True

    except Exception as e:
        print(f"[ERROR] Erreur lors de l'export Word structuré: {e}")
        import traceback
        traceback.print_exc()
        return False