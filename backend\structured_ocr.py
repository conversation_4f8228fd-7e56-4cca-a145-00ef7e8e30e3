"""
Module de reconstruction structurée des résultats OCR avec détection de mise en page
Combine la détection de zones avec l'OCR pour produire un résultat structuré
"""
import os
import json
import logging
import tempfile
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

# Configuration de l'environnement
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"
os.environ["CUDA_VISIBLE_DEVICES"] = ""
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"

import warnings
warnings.filterwarnings("ignore")

import cv2
import numpy as np
from PIL import Image

# Imports des modules OCR existants
from backend.layout_detector import create_layout_detector, LayoutDetector
from backend.ocr_tesseract import ocr_tesseract
from backend.preprocessing import preprocess_image

# Configuration du logging
logger = logging.getLogger(__name__)


class StructuredOCRProcessor:
    """
    Processeur OCR structuré qui combine détection de mise en page et OCR
    """
    
    def __init__(self, layout_confidence: float = 0.5, enable_debug: bool = False):
        """
        Initialise le processeur OCR structuré
        
        Args:
            layout_confidence: Seuil de confiance pour la détection de mise en page
            enable_debug: Active la génération d'images de debug
        """
        self.layout_detector = create_layout_detector(layout_confidence)
        self.enable_debug = enable_debug
        
        # Statistiques de traitement
        self.stats = {
            "total_zones": 0,
            "text_zones": 0,
            "table_zones": 0,
            "title_zones": 0,
            "figure_zones": 0,
            "processing_time": 0
        }
    
    def process_image(self, image_path: str) -> Dict[str, Any]:
        """
        Traite une image avec détection de mise en page et OCR structuré
        
        Args:
            image_path: Chemin vers l'image à traiter
            
        Returns:
            Résultat structuré avec zones et contenu OCR
        """
        import time
        start_time = time.time()
        
        try:
            logger.info(f"Début du traitement structuré pour: {image_path}")
            
            # 1. Détection de la mise en page
            zones = self.layout_detector.detect_layout(image_path)
            
            # 2. Traitement OCR pour chaque zone
            structured_result = {
                "image_path": image_path,
                "total_zones": len(zones),
                "zones": [],
                "full_text": "",
                "metadata": {
                    "layout_detection_available": self.layout_detector.is_available(),
                    "processing_timestamp": time.time()
                }
            }
            
            # 3. Traiter chaque zone détectée
            for zone in zones:
                zone_result = self._process_zone(image_path, zone)
                if zone_result:
                    structured_result["zones"].append(zone_result)
            
            # 4. Reconstruire le texte complet dans l'ordre logique
            structured_result["full_text"] = self._reconstruct_full_text(structured_result["zones"])
            
            # 5. Générer des statistiques
            self._update_stats(structured_result["zones"])
            structured_result["stats"] = self.stats.copy()
            
            # 6. Sauvegarder l'image de debug si activé
            if self.enable_debug:
                debug_path = self.layout_detector.save_zone_debug(image_path, zones)
                structured_result["debug_image"] = debug_path
            
            # 7. Sauvegarder le résultat JSON
            json_path = self._save_structured_result(structured_result)
            structured_result["json_path"] = json_path
            
            processing_time = time.time() - start_time
            self.stats["processing_time"] = processing_time
            
            logger.info(f"Traitement terminé en {processing_time:.2f}s - {len(zones)} zones traitées")
            return structured_result
            
        except Exception as e:
            logger.error(f"Erreur lors du traitement structuré: {e}")
            # Retourner un résultat minimal en cas d'erreur
            return self._create_fallback_result(image_path, str(e))
    
    def _process_zone(self, image_path: str, zone: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Traite une zone spécifique avec OCR
        
        Args:
            image_path: Chemin vers l'image source
            zone: Informations de la zone à traiter
            
        Returns:
            Résultat OCR pour la zone ou None en cas d'erreur
        """
        try:
            zone_type = zone["type"]
            bbox = zone["bbox"]
            
            logger.debug(f"Traitement zone {zone['id']} de type '{zone_type}'")
            
            # Extraire l'image de la zone
            zone_image = self.layout_detector.extract_zone_image(image_path, bbox)
            if zone_image is None:
                logger.warning(f"Impossible d'extraire la zone {zone['id']}")
                return None
            
            # Sauvegarder temporairement l'image de la zone
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name
                cv2.imwrite(temp_path, zone_image)
            
            try:
                # Appliquer l'OCR selon le type de zone avec préprocessing adaptatif
                if zone_type == "title":
                    # Préprocessing spécialisé pour les titres
                    processed_image = preprocess_image(temp_path, method="title_optimized")
                    lines, confidences = ocr_tesseract(processed_image, return_conf=True)

                elif zone_type in ["text", "list"]:
                    # Préprocessing optimisé pour le texte normal
                    processed_image = preprocess_image(temp_path, method="tesseract_optimized")
                    lines, confidences = ocr_tesseract(processed_image, return_conf=True)

                elif zone_type == "table":
                    # Préprocessing spécialisé pour les tableaux
                    processed_image = preprocess_image(temp_path, method="table_optimized")
                    lines, confidences = ocr_tesseract(processed_image, return_conf=True)

                elif zone_type == "figure":
                    # Pour les figures, on peut essayer d'extraire du texte (légendes, etc.)
                    # Utiliser un préprocessing doux pour préserver les détails
                    processed_image = preprocess_image(temp_path, method="enhanced")
                    lines, confidences = ocr_tesseract(processed_image, return_conf=True)

                else:
                    # Type inconnu, traitement par défaut
                    processed_image = preprocess_image(temp_path, method="tesseract_optimized")
                    lines, confidences = ocr_tesseract(processed_image, return_conf=True)
                
                # Nettoyer le fichier temporaire
                os.unlink(temp_path)
                
                # Construire le résultat de la zone
                zone_result = {
                    "id": zone["id"],
                    "type": zone_type,
                    "bbox": bbox,
                    "layout_confidence": zone["confidence"],
                    "area": zone["area"],
                    "content": {
                        "lines": lines,
                        "confidences": confidences,
                        "avg_confidence": sum(confidences) / len(confidences) if confidences else 0,
                        "text": "\n".join(lines),
                        "char_count": sum(len(line) for line in lines),
                        "line_count": len(lines)
                    }
                }
                
                return zone_result
                
            except Exception as e:
                # Nettoyer le fichier temporaire en cas d'erreur
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                raise e
                
        except Exception as e:
            logger.error(f"Erreur lors du traitement de la zone {zone.get('id', 'unknown')}: {e}")
            return None
    
    def _reconstruct_full_text(self, zones: List[Dict[str, Any]]) -> str:
        """
        Reconstruit le texte complet à partir des zones dans l'ordre logique
        
        Args:
            zones: Liste des zones traitées
            
        Returns:
            Texte complet reconstruit
        """
        full_text_parts = []
        
        # Trier les zones par position (déjà fait dans layout_detector, mais on s'assure)
        sorted_zones = sorted(zones, key=lambda z: (z["bbox"]["y1"], z["bbox"]["x1"]))
        
        for zone in sorted_zones:
            zone_type = zone["type"]
            content = zone["content"]
            
            if not content["text"].strip():
                continue
            
            # Formatage selon le type de zone
            if zone_type == "title":
                # Titres avec séparateurs
                full_text_parts.append(f"\n=== {content['text'].strip()} ===\n")
                
            elif zone_type == "table":
                # Tableaux avec marqueurs
                full_text_parts.append(f"\n[TABLEAU]\n{content['text']}\n[/TABLEAU]\n")
                
            elif zone_type == "list":
                # Listes avec indentation
                list_lines = [f"  • {line.strip()}" for line in content["lines"] if line.strip()]
                full_text_parts.append("\n".join(list_lines) + "\n")
                
            elif zone_type == "figure":
                # Figures avec description
                if content["text"].strip():
                    full_text_parts.append(f"\n[FIGURE: {content['text'].strip()}]\n")
                
            else:  # text ou autre
                # Texte normal
                full_text_parts.append(content["text"] + "\n")
        
        return "\n".join(full_text_parts).strip()
    
    def _update_stats(self, zones: List[Dict[str, Any]]) -> None:
        """
        Met à jour les statistiques de traitement
        
        Args:
            zones: Liste des zones traitées
        """
        self.stats["total_zones"] = len(zones)
        self.stats["text_zones"] = sum(1 for z in zones if z["type"] == "text")
        self.stats["table_zones"] = sum(1 for z in zones if z["type"] == "table")
        self.stats["title_zones"] = sum(1 for z in zones if z["type"] == "title")
        self.stats["figure_zones"] = sum(1 for z in zones if z["type"] == "figure")
    
    def _save_structured_result(self, result: Dict[str, Any]) -> str:
        """
        Sauvegarde le résultat structuré en JSON
        
        Args:
            result: Résultat structuré à sauvegarder
            
        Returns:
            Chemin vers le fichier JSON sauvegardé
        """
        try:
            # Créer le dossier de sortie
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            # Nom du fichier basé sur l'image source
            image_name = Path(result["image_path"]).stem
            json_path = output_dir / f"structured_result_{image_name}.json"
            
            # Sauvegarder avec indentation pour la lisibilité
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Résultat structuré sauvegardé: {json_path}")
            return str(json_path)
            
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde JSON: {e}")
            return ""
    
    def _create_fallback_result(self, image_path: str, error_message: str) -> Dict[str, Any]:
        """
        Crée un résultat de fallback en cas d'erreur
        
        Args:
            image_path: Chemin vers l'image
            error_message: Message d'erreur
            
        Returns:
            Résultat minimal de fallback
        """
        return {
            "image_path": image_path,
            "total_zones": 0,
            "zones": [],
            "full_text": "",
            "error": error_message,
            "metadata": {
                "layout_detection_available": False,
                "processing_timestamp": time.time(),
                "fallback_mode": True
            },
            "stats": {
                "total_zones": 0,
                "text_zones": 0,
                "table_zones": 0,
                "title_zones": 0,
                "figure_zones": 0,
                "processing_time": 0
            }
        }


def process_image_structured(image_path: str, layout_confidence: float = 0.5, enable_debug: bool = False) -> Dict[str, Any]:
    """
    Function utilitaire pour traiter une image avec OCR structuré
    
    Args:
        image_path: Chemin vers l'image à traiter
        layout_confidence: Seuil de confiance pour la détection de mise en page
        enable_debug: Active la génération d'images de debug
        
    Returns:
        Résultat structuré
    """
    processor = StructuredOCRProcessor(layout_confidence, enable_debug)
    return processor.process_image(image_path)


# Test de base si exécuté directement
if __name__ == "__main__":
    # Configuration du logging pour les tests
    logging.basicConfig(level=logging.INFO)
    
    # Test avec une image d'exemple si disponible
    test_images = ["images/exemple1.png", "images/exemple2.jpg"]
    for img_path in test_images:
        if Path(img_path).exists():
            print(f"\nTest OCR structuré avec {img_path}:")
            result = process_image_structured(img_path, enable_debug=True)
            print(f"Zones détectées: {result['total_zones']}")
            print(f"Texte reconstruit: {len(result['full_text'])} caractères")
            if result.get('json_path'):
                print(f"Résultat sauvegardé: {result['json_path']}")
            break
